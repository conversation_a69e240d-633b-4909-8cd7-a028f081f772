apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: dsw-leader-election-role
  namespace: {{ .Release.Namespace }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - configmaps/status
  verbs:
  - get
  - update
  - patch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: dsw-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - events
  - persistentvolumeclaims
  - pods
  - secrets
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - events/status
  - pods/status
  - secrets/status
  - services/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - get
  - list
- apiGroups:
  - ""
  resources:
  - nodes/status
  - persistentvolumeclaims/status
  verbs:
  - get
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - get
  - list
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - deployments/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - base.pai.alibaba-inc.com
  resources:
  - configs
  - credentials
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - base.pai.alibaba-inc.com
  resources:
  - credentials/status
  verbs:
  - get
- apiGroups:
  - batch
  resources:
  - jobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - batch
  resources:
  - jobs/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - dlc.alibaba.com
  resources:
  - datasources
  - paiquotas
  - resourcegroups
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - dlc.alibaba.com
  resources:
  - datasources/status
  - paiquotas/status
  - resourcegroups/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - dsw.alibaba.com
  resources:
  - containersnapshots
  - credentials
  - dswinstances
  - idleinstancecullers
  - images
  - nasvolumes
  - notebooks
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - dsw.alibaba.com
  resources:
  - containersnapshots/status
  - credentials/status
  - dswinstances/status
  - idleinstancecullers/status
  - images/status
  - nasvolumes/status
  - notebooks/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - eas.alibaba-inc.k8s.io
  resources:
  - tenantresources
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - eas.alibaba-inc.k8s.io
  resources:
  - tenantresources/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - forwarder.pai.alibaba-inc.com
  resources:
  - instanceforwarders
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - gateway.solo.io
  resources:
  - gateways
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - gateway.solo.io
  resources:
  - gateways/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - quotas.alibabacloud.com
  resources:
  - quotas
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - quotas.alibabacloud.com
  resources:
  - quotas/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - rbac.authorization.k8s.io
  resources:
  - rolebindings
  - roles
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - rbac.authorization.k8s.io
  resources:
  - rolebindings/status
  - roles/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - runtime.alibabacloud.com
  resources:
  - persistentcontainerclaims
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - runtime.alibabacloud.com
  resources:
  - persistentcontainerclaims/status
  verbs:
  - get
- apiGroups:
  - scheduling.eas.alicloud.com
  resources:
  - externalclusters
  - virtualresources
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - scheduling.x-k8s.io
  resources:
  - queueunits
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - scheduling.x-k8s.io
  resources:
  - queueunits/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - spot.alibaba.com
  resources:
  - spotinstancetypes
  - spotjobbids
  verbs:
  - create
  - delete
  - deletecollection
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - spot.alibaba.com
  resources:
  - spotinstancetypes/status
  - spotjobbids/status
  verbs:
  - get
  - list
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: dsw-leader-election-rolebinding
  namespace: {{ .Release.Namespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: dsw-leader-election-role
subjects:
- kind: ServiceAccount
  name: default
  namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: dsw-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: dsw-manager-role
subjects:
- kind: ServiceAccount
  name: default
  namespace: {{ .Release.Namespace }}
