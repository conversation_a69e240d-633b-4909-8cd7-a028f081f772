{{- if.Values.deployConfig.deployWorkload }}
apiVersion: v1
kind: Secret
type: Opaque
metadata:
  name: {{ include "pai-dsw-operator.resourceName" (list "dsw-controller-kms-secret" .) }}
  namespace: {{ .Release.Namespace }}
data:
  {{- if kindIs "string" .Values.appConfig.kmsConfig }}
  kmsConfig.json: {{ .Values.appConfig.kmsConfig | b64enc }}
  {{- else }}
  kmsConfig.json: {{ .Values.appConfig.kmsConfig | toJson | b64enc }}
  {{- end }}
{{- end}}