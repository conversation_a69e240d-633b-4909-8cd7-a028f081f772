{{- if.Values.deployConfig.deployWorkload }}
apiVersion: apps/v1
kind: Deployment
metadata:
  {{- if eq .Values.shardName "shard1"}}
  name: pai-dsw-operator-controller
  {{- else}}
  name: pai-dsw-operator-controller-{{ .Values.shardName}}
  {{- end}}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: pai-dsw-operator-controller
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: pai-dsw-operator-controller
  replicas:  {{ index .Values.deployConfig .Values.shardName "replicas" }}
  template:
    metadata:
      {{- if index .Values.deployConfig .Values.shardName "enableGrayRelease" }}
      annotations:
        checksum/grayReleaseConfig: {{ index .Values.deployConfig .Values.shardName "grayReleaseConfig" | toJson | sha256sum }}
      {{- end }}
      labels:
        app.kubernetes.io/name: pai-dsw-operator-controller
    spec:
      containers:
      - command:
        - /workspace/manager
        args:
          {{/* common configs */}}
          - --enable-leader-election=true
          - --leader-election-namespace={{ .Release.Namespace }}
          - --cluster-type={{ required "Helm value clusterType is required!" .Values.appConfig.clusterType }}
          {{- with .Values.appConfig.region }}
          - --region={{ . }}
          {{- end }}
          {{- with .Values.appConfig.ackClusterId }}
          - --ack-cluster-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.maxConcurrentReconciles }}
          - --max-concurrent-reconciles={{ . }}
          {{- end }}

          {{/* account infos */}}
          {{- with .Values.appConfig.resourceAccountId }}
          - --resource-account-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.accessKeyId }}
          - --access-key-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.accessKeySecret }}
          - --access-key-secret={{ . }}
          {{- end }}
          {{- with .Values.appConfig.serviceAccountId }}
          - --service-account-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.serviceAccountAccessKeyId }}
          - --service-account-access-key-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.serviceAccountAccessKeySecret }}
          - --service-account-access-key-secret={{ . }}
          {{- end }}

          {{/* configs */}}
          {{- with .Values.appConfig.stsEndpoint }}
          - --sts-endpoint={{ . }}
          {{- end }}
          {{- with .Values.appConfig.nasInnerDomain }}
          - --nas-inner-domain={{ . }}
          {{- end }}
          {{- with .Values.appConfig.cpfsRegionId }}
          - --cpfs-region-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.runtime }}
          - --runtime={{ . }}
          {{- end }}
          {{- with .Values.appConfig.containerdPath }}
          - --containerd-path={{ . }}
          {{- end }}
          {{- with .Values.deployConfig.dockerRegistryAuth }}
          - --docker-registry-auth={{ . }}
          {{- end }}
          {{- with .Values.appConfig.jobSchedulePolicy }}
          - --job-schedule-policy={{ . }}
          {{- end }}
          {{- with .Values.appConfig.createTimeOutInMinute }}
          - --create-time-out-in-minute={{ . }}
          {{- end }}
          {{- with .Values.appConfig.queueTimeOutInMinute }}
          - --queue-time-out-in-minute={{ . }}
          {{- end }}
          {{- with .Values.appConfig.recoverTimeOutInMinute }}
          - --recover-time-out-in-minute={{ . }}
          {{- end }}
          {{- with .Values.appConfig.saveImageTimeOutInMinute }}
          - --save-image-time-out-in-minute={{ . }}
          {{- end }}
          {{- with .Values.appConfig.savingJobPreparingTimeOutInMinute }}
          - --saver-job-preparing-time-out-in-minute={{ . }}
          {{- end }}
          {{- with .Values.appConfig.committingTimeOutInMinute }}
          - --committing-time-out-in-minute={{ . }}
          {{- end }}
          {{- with .Values.appConfig.tenantScopeKey }}
          - --tenant-scope-key={{ . }}
          {{- end }}
          {{- with .Values.appConfig.acrInstanceName }}
          - --acr-instance-name={{ . }}
          {{- end }}
          {{- with .Values.appConfig.localBinPath }}
          - --local-bin-path={{ . }}
          {{- end }}
          {{- with .Values.appConfig.sockPath }}
          - --sock-path={{ . }}
          {{- end }}
          {{- with .Values.appConfig.useQuotaIdAsNamespace }}
          - --use-quota-id-as-namespace={{ . }}
          {{- end }}
          {{- with .Values.appConfig.eciSecondaryEniCidr }}
          - --eci-secondary-eni-cidr={{ . }}
          {{- end }}
          {{- with .Values.appConfig.eciVMImageId }}
          - --eci-vm-image-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.dlinkPolicy }}
          - --dlink-policy={{ . }}
          {{- end }}
          {{- with .Values.appConfig.prometheusEndpointUrl }}
          - --prometheus-endpoint-url={{ . }}
          {{- end }}
          {{- with .Values.appConfig.stepInPrometheusRangeQueries }}
          - --step-in-prometheus-range-queries={{ . }}
          {{- end }}
          {{- with .Values.appConfig.stepInListRangeMetrics }}
          - --step-in-list-range-metrics={{ . }}
          {{- end }}
          {{- with .Values.appConfig.metricsPullInterval }}
          - --metrics-pull-interval={{ . }}
          {{- end }}
          {{- with .Values.appConfig.metricsSourceType }}
          - --metrics-source-type={{ . }}
          {{- end }}
          {{- with .Values.appConfig.dswToECIRole }}
          - --dsw-to-eci-role={{ . }}
          {{- end }}
          {{- with .Values.appConfig.serviceRoleChainRole }}
          - --service-role-chain-role={{ . }}
          {{- end }}
          {{- with .Values.appConfig.privateZoneNamePrefix }}
          - --private-zone-name-prefix={{ . }}
          {{- end }}
          {{- with .Values.appConfig.maxWritableLayerSize }}
          - --max-writable-layer-size={{ . }}
          {{- end }}
          {{- with .Values.appConfig.workspaceEndpoint }}
          - --workspace-endpoint={{ . }}
          {{- end }}
          {{- with .Values.appConfig.disableDefaultACREndpoint }}
          - --disable-default-acr={{ . }}
          {{- end }}
          {{- with .Values.appConfig.endpointType }}
          - --endpoint-type={{ . }}
          {{- end }}
          {{- with .Values.appConfig.endpointNetwork }}
          - --endpoint-network={{ . }}
          {{- end }}
          {{- with .Values.appConfig.vpcId }}
          - --vpc-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.vswitchId }}
          - --vswitch-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.securityGroupId }}
          - --security-group-id={{ . }}
          {{- end }}
          {{- with .Values.appConfig.podLimitedLockedMemory }}
          - --pod-limited-locked-memory={{ . }}
          {{- end }}
          {{- with .Values.appConfig.podLimitedNoFiles }}
          - --pod-limited-no-files={{ . }}
          {{- end }}
          {{- with .Values.appConfig.ingressAuthUrl }}
          - --ingress-auth-url={{ . }}
          {{- end }}
          {{- with .Values.appConfig.ingressAuthSignin }}
          - --ingress-auth-signin={{ . }}
          {{- end }}
          {{- with .Values.appConfig.virtualGpuConfigType }}
          - --virtual-gpu-config-type={{ . }}
          {{- end }}
          {{- with .Values.appConfig.clusterConfigSource }}
          - --cluster-config-source={{ . }}
          {{- end }}
          {{- with .Values.appConfig.workloadQueueType }}
          - --workload-queue-type={{ . }}
          {{- end }}
          {{- if and (.Values.appConfig.region) (.Values.deployConfig.dswImageSaverImage) }}
          {{- if and (eq .Values.appConfig.clusterType "asi-lingjun") (.Values.deployConfig.dockerRegistryPrefix) }}
          - --dsw-image-saver-image={{ .Values.deployConfig.dockerRegistryPrefix }}.{{ .Values.appConfig.region }}.cr.aliyuncs.com/{{ .Values.deployConfig.dockerRegistryNamespace }}/{{ .Values.deployConfig.dswImageSaverImage }}
          {{- else if (.Values.deployConfig.dockerRegistryVpcPrefix) }}
          - --dsw-image-saver-image={{ .Values.deployConfig.dockerRegistryVpcPrefix }}.{{ .Values.appConfig.region }}.cr.aliyuncs.com/{{ .Values.deployConfig.dockerRegistryNamespace }}/{{ .Values.deployConfig.dswImageSaverImage }}
          {{- end }}
          {{- end }}
          {{- if and (.Values.deployConfig.dockerRegistryVpcPrefix) (.Values.appConfig.region) (.Values.deployConfig.proxySidecarImage) }}
          - --proxy-sidecar-image={{ .Values.deployConfig.dockerRegistryVpcPrefix }}.{{ .Values.appConfig.region }}.cr.aliyuncs.com/{{ .Values.deployConfig.dockerRegistryNamespace }}/{{ .Values.deployConfig.proxySidecarImage }}
          {{- end }}
          {{- if and (.Values.deployConfig.dockerRegistryVpcPrefix) (.Values.appConfig.region) (.Values.deployConfig.setupSidecarImage) }}
          - --setup-sidecar-image={{ .Values.deployConfig.dockerRegistryVpcPrefix }}.{{ .Values.appConfig.region }}.cr.aliyuncs.com/{{ .Values.deployConfig.dockerRegistryNamespace }}/{{ .Values.deployConfig.setupSidecarImage }}
          {{- end }}
          {{- if and (.Values.deployConfig.dockerRegistryVpcPrefix) (.Values.appConfig.region) (.Values.deployConfig.sidecarImage) }}
          - --sidecar-image={{ .Values.deployConfig.dockerRegistryVpcPrefix }}.{{ .Values.appConfig.region }}.cr.aliyuncs.com/{{ .Values.deployConfig.dockerRegistryNamespace }}/{{ .Values.deployConfig.sidecarImage }}
          {{- end }}
          {{- if and (.Values.deployConfig.dockerRegistryVpcPrefix) (.Values.appConfig.region) (.Values.deployConfig.dindSidecarImage) }}
          - --dind-sidecar-image={{ .Values.deployConfig.dockerRegistryVpcPrefix }}.{{ .Values.appConfig.region }}.cr.aliyuncs.com/{{ .Values.deployConfig.dockerRegistryNamespace }}/{{ .Values.deployConfig.dindSidecarImage }}
          {{- end }}
          {{/* enable flags */}}
          {{- with .Values.appConfig.enableCpfs }}
          - --enable-cpfs={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableHideCPFSSnapShot }}
          - --enable-hide-cpfs-snapshot={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableCpfsPovOnHost }}
          - --enable-cpfs-povonhost={{ . }}
          {{- end }}
          {{- with .Values.appConfig.onlySupportPovOnHostInfraStructure }}
          - --only-support-pov-on-host-infrastructure={{. }}
          {{- end }}
          {{- with .Values.appConfig.enablePaiCredential }}
          - --enable-pai-credential={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableOnDemandImage }}
          - --enable-on-demand-image={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enablePullAcrEEImage }}
          - --enable-pull-acree-image={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableRenewAcrTempAuthToken }}
          - --enable-renew-acr-temp-auth-token={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableIdleInstanceCuller }}
          - --enable-idle-instance-culler={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enablePrivateZone }}
          - --enable-private-zone={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableAsiQuota }}
          - --enable-asi-quota={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableWritableLayerSizeCheck }}
          - --enable-writable-layer-size-check={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableNodeErrorReport }}
          - --enable-node-error-report={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableKmsDecryption }}
          - --enable-kms-decryption={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableSpotMetering }}
          - --enable-spot-metering={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enablePaiMetrics }}
          - --enable-pai-metrics={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enablePullImageByNsm }}
          - --enable-pull-image-by-nsm={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableGpuSideCarInjection }}
          - --enable-gpu-sidecar-injection={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableRDMASideCarInjection }}
          - --enable-rdma-sidecar-injection={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableAliyunEniResource }}
          - --enable-aliyun-eni-resource={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableSyncSecurityGroupRule }}
          - --enable-sync-security-group-rule={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableGraySyncSecurityGroupRule }}
          - --enable-gray-sync-security-group-rule={{ . }}
          {{- end }}
          {{- with .Values.appConfig.syncSecurityGroupRuleUsersWhiteList }}
          - --sync-security-group-rule-users-white-list={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableMultiCluster }}
          - --enable-multi-cluster={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableVirtualGpuConfig }}
          - --enable-virtual-gpu-config={{ . }}
          {{- end }}
          {{- with .Values.appConfig.enableGpuExporter }}
          - --enable-gpu-exporter={{ . }}
          {{- end }}
          {{/* capabilities */}}
          {{- if .Capabilities.APIVersions.Has "networking.k8s.io/v1beta1/Ingress" -}}
          - --ingress-version=v1beta1
          {{ end }}
          {{- with .Values.appConfig.ingressHost }}
          - --ingress-host={{ . }}
          {{- end }}
          - --enable-gray-release={{ index .Values.deployConfig .Values.shardName "enableGrayRelease" }}
          - --enable-gray-watcher={{ index .Values.deployConfig .Values.shardName "enableGrayWatcher" }}
          - --gray-module={{ .Values.deployConfig.grayModule }}
          {{- with .Values.shardName }}
          - --shard-name={{ . }}
          {{- end }}
          {{/* user command */}}
          {{- if .Values.appConfig.userCommandOssBucket }}
          - --user-command-oss-bucket={{ .Values.appConfig.userCommandOssBucket }}
          {{- else }}
          - --user-command-oss-bucket=pai-user-command-{{ .Values.appConfig.region }}
          {{- end }}
          {{ with .Values.appConfig.enableUserCommand }}
          - --enable-user-command={{ . }}
          - --user-command-oss-endpoint=http://oss-{{ $.Values.appConfig.region }}-internal.aliyuncs.com
          {{- end }}
          {{- with .Values.appConfig.enableResourceManagerGateway }}
          - --enable-resource-manager-gateway={{ . }}
          {{- end }}
          {{- with .Values.appConfig.rmcoreEndpoint }}
          - --rmcore-endpoint={{ . }}
          {{- end }}
        image: "{{ .Values.deployConfig.dswOperatorImageRepository }}:{{ .Values.deployConfig.dswOperatorImageTag }}"
        imagePullPolicy: Always
        name: manager
        env:
          - name: ARMS_ENABLE
            value: "{{ .Values.appConfig.instgo.enabled }}"
          - name: ARMS_APP_NAME
            value: "{{ .Values.appConfig.instgo.appName }}"
          - name: ARMS_REGION_ID
            value: "{{ .Values.appConfig.instgo.regionID }}"
          - name: ARMS_LICENSE_KEY
            value: "{{ .Values.appConfig.instgo.licenseKey }}"
          - name: TZ
            value: "Asia/Shanghai"
            {{- if eq .Values.appConfig.ackType "pro_pure" }}
          - name: REGISTRY_USERNAME
            valueFrom:
              secretKeyRef:
                name: docker-registry-secret
                key: DOCKER_REGISTRY_USERNAME
          - name: REGISTRY_PASSWORD
            valueFrom:
              secretKeyRef:
                name: docker-registry-secret
                key: DOCKER_REGISTRY_PASSWORD
            {{ end }}
        ports:
          - containerPort: 8081
            name: metrics
            protocol: TCP
        resources:
        {{- toYaml .Values.deployConfig.resources | nindent 10 }}
        volumeMounts:
          - mountPath: /feature-gate
            name: feature-gate
            readOnly: true
          - name: config-volume
            mountPath: /workspace/dsw-config
            readOnly: true
          - name: kms-secret-volume
            mountPath: /workspace/dsw-kms-secret
      terminationGracePeriodSeconds: 10
      {{- with .Values.asiTolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
      - name: config-volume
        configMap:
          name: {{ include "pai-dsw-operator.resourceName" (list "dsw-config" .) }}
      - name: kms-secret-volume
        secret:
          secretName: {{ include "pai-dsw-operator.resourceName" (list "dsw-controller-kms-secret" .) }}
      - configMap:
          defaultMode: 420
          items:
            - key: feature-gate.yaml
              path: feature-gate.yaml
          name: {{ include "pai-dsw-operator.resourceName" (list "pai-dsw-operator-feature-gate" .) }}
        name: feature-gate
{{- end}}