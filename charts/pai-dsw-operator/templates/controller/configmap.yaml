{{- if.Values.deployConfig.deployWorkload }}
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ include "pai-dsw-operator.resourceName" (list "pai-dsw-operator-feature-gate" .) }}
  namespace: {{ .Release.Namespace }}
data:
  feature-gate.yaml: |
    ---
    {{- with .Values.appConfig.featureGate }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "pai-dsw-operator.resourceName" (list "dsw-config" .) }}
  namespace: {{ .Release.Namespace }}
data:
  {{- if kindIs "string" (index .Values.deployConfig .Values.shardName "grayReleaseConfig") }}
  grayReleaseConfig.json: {{ index .Values.deployConfig .Values.shardName "grayReleaseConfig" }}
  {{- else }}
  grayReleaseConfig.json: {{ index .Values.deployConfig .Values.shardName "grayReleaseConfig" | toJson | quote }}
  {{- end }}

{{- end}}