shardName: "shard1"

deployConfig:
  useFiles:
    - files/*
  deployWorkload: true

  dswImageSaverImage:
  dockerRegistryAuth:
  dswOperatorImageRepository:
  dswOperatorImageTag:

  # Docker registry configurations
  dockerRegistryPrefix: "dsw-registry"
  dockerRegistryVpcPrefix: "dsw-registry-vpc"
  dockerRegistryNamespace: "pai-common"

  # Sidecar images
  proxySidecarImage:
  setupSidecarImage:
  dindSidecarImage:
  sidecarImage:

  # Shard configurations
  shard1:
    replicas: 1
    enableGrayRelease: false
    enableGrayWatcher: false
    grayReleaseConfig: {}
  shard2:
    replicas: 1
    enableGrayRelease: false
    enableGrayWatcher: false
    grayReleaseConfig: { }
  grayModule: ""

appConfig:
  # valid values are: ack-mts, light, asi-inner, asi-cloud
  clusterType: "ack-mts"
  region:
  region_id: "cn-hangzhou"
  ackClusterId:
  ackType: pro
  
  # Access keys and authentication
  accessKeyId:
  accessKeySecret:
  resourceAccountId:
  serviceAccountAccessKeyId:
  serviceAccountAccessKeySecret:
  serviceAccountId:
  stsEndpoint: "sts.aliyuncs.com"
  
  # Roles
  dswToECIRole: AliyunPAIDSWToECIRole
  serviceRoleChainRole: RoleToAssumeCustomerRole
  
  # CPFS configurations
  enableHideCPFSSnapShot:
  enableCpfsPovOnHost:
  onlySupportPovOnHostInfraStructure:
  cpfsRegionId:
  enableCpfs: false
  
  # NAS configurations
  nasInnerDomain:
  
  # Network configurations
  vpcId: ""
  securityGroupId: ""
  vswitchId: ""
  endpointNetwork: ""
  enablePrivateZone: false
  privateZoneNamePrefix: "svc.cluster.local."
  eciSecondaryEniCidr: *************/32,*************/32

  disableDefaultACREndpoint: false
  
  # ACR configurations
  acrInstanceName: dsw
  enablePullAcrEEImage: false
  enableRenewAcrTempAuthToken: false
  
  # Ingress configurations
  ingressAuthUrl:
  ingressAuthSignin:
  ingressHost: ""
  
  # Tenant and namespace configurations
  tenantScopeKey: tenant.dsw.alibaba-inc.com
  useQuotaIdAsNamespace: false
  
  # Job and workload configurations
  jobSchedulePolicy: default
  dlinkPolicy: default
  workloadQueueType:
  
  # Feature flags
  enableOnDemandImage: false
  enableAsiQuota: false
  enableIdleInstanceCuller: false
  enablePaiCredential: true
  enableSpotMetering: false
  enablePaiMetrics: false
  enableWritableLayerSizeCheck: true
  enableAliyunEniResource: false
  enableKmsDecryption: false
  enableSyncSecurityGroupRule: false
  enableGraySyncSecurityGroupRule: false
  
  # GPU and virtual GPU configurations
  virtualGpuConfigType:
  enableVirtualGpuConfig:
  enableGpuExporter:
  
  # Runtime configurations
  runtime:
  sockPath:
  localBinPath:
  containerdPath:
  
  # Multi-cluster configurations
  enableMultiCluster:
  clusterConfigSource:
  
  # ECI configurations
  eciVMImageId:
  
  # Resource limits
  maxWritableLayerSize: "10Gi"
  podLimitedLockedMemory: "unlimited"
  podLimitedNoFiles: "102400"
  
  # Metrics configurations
  prometheusEndpointUrl:
  stepInPrometheusRangeQueries: 30s
  stepInListRangeMetrics: 10s
  metricsPullInterval: 60
  metricsSourceType: ""
  
  # Workspace configurations
  workspaceEndpoint: ""
  
  # Feature gate
  featureGate:
  
  # KMS configurations
  kmsConfig: {}
  
  # User command configurations
  userCommandOssEndpoint: ""
  userCommandOssBucket: ""
  enableUserCommand: false
  
  # Security group sync configurations
  syncSecurityGroupRuleUsersWhiteList: ""

  instgo:
    enabled: "true"
    appName:
    regionID:
    licenseKey:

  # Resource configurations
  maxConcurrentReconciles: 10

  # Timeout configurations
  createTimeOutInMinute: 20
  queueTimeOutInMinute: 20
  recoverTimeOutInMinute: 10080
  saveImageTimeOutInMinute: 30

  # Feature flags for deployment
  enablePullImageByNsm:
  enableRDMASideCarInjection: false
  enableGpuSideCarInjection: false
  enableNodeErrorReport: false

  savingJobPreparingTimeOutInMinute:
  committingTimeOutInMinute:
  enableResourceManagerGateway: false
  rmcoreEndpoint: ""