apiVersion: v1
kind: ServiceAccount
metadata:
  name: pai-gray-operator
  namespace: pai-gray-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pai-gray-operator-leader-election-role
  namespace: pai-gray-system
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  name: pai-gray-operator-role
rules:
- apiGroups:
  - ""
  resources:
  - pods
  - events
  - configmaps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - events/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - secrets/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - dlc.alibaba.com
  resources:
  - datasources
  - '*'
  verbs:
  - '*'
- apiGroups:
  - dsw.alibaba.com
  resources:
  - '*'
  verbs:
  - '*'
- apiGroups:
  - pai.alibabacloud.com
  resources:
    - '*'
  verbs:
    - '*'
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - validatingwebhookconfigurations
  - mutatingwebhookconfigurations
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: pai-gray-operator-metrics-reader
rules:
- nonResourceURLs:
  - /metrics
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: pai-gray-operator-proxy-role
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: pai-gray-operator-leader-election-rolebinding
  namespace: pai-gray-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: pai-gray-operator-leader-election-role
subjects:
- kind: ServiceAccount
  name: pai-gray-operator
  namespace: pai-gray-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: pai-gray-operator-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: pai-gray-operator-role
subjects:
- kind: ServiceAccount
  name: pai-gray-operator
  namespace: pai-gray-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: pai-gray-operator-proxy-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: pai-gray-operator-proxy-role
subjects:
- kind: ServiceAccount
  name: pai-gray-operator
  namespace: pai-gray-system
---
apiVersion: v1
kind: Service
metadata:
  labels:
    control-plane: pai-gray-operator
  name: pai-gray-operator-metrics-service
  namespace: pai-gray-system
spec:
  ports:
  - name: https
    port: 8443
    targetPort: https
  selector:
    sigma.ali/app-name: pai-gray-operator
