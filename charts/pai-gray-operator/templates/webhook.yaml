apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: pai-gray-operator-webhook
webhooks:
  - name: gray.pai.alibabacloud.io
    rules:
      - apiGroups:   ["dlc.alibaba.com"]
        apiVersions: ["v1"]
        operations:  ["CREATE"]
        resources:   ["datasources","dlcinstances","tensorboards","paiquotas","resourcegroups","dlcjobs","datacacheservices"]
      - apiGroups:   ["dsw.alibaba.com"]
        apiVersions: ["v1"]
        operations:  ["CREATE"]
        resources:   ["dswinstances","containersnapshots","credentials","idleinstancecullers","notebooks","images","nasvolumes"]
      - apiGroups:   ["pai.alibabacloud.com"]
        apiVersions: ["v1alpha1"]
        operations:  ["CREATE"]
        resources:   ["generaljobs"]
    clientConfig:
      service:
        namespace: pai-gray-system
        name: pai-gray-operator-webhook
        path: "/pai-gray-operator-mutating-webhook-v1"
        port: 9443
      caBundle: Cg==
    timeoutSeconds: 20
    failurePolicy: Ignore
    sideEffects: None
    admissionReviewVersions: ["v1","v1beta1"]
    # objectSelector:
    #   matchExpressions:
    #     - key: "ali/metric-tenant-id"
    #       operator: "Exists"