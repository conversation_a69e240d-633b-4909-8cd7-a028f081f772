apiVersion: v1
kind: ServiceAccount
metadata:
  name: eas-cluster
  namespace: {{ .Release.Namespace }}
  labels:
{{ include "cluster-controller.labels" . | indent 4 }}
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: eas-cluster-controller-role
  labels:
{{ include "cluster-controller.labels" . | indent 4 }}
rules:
- apiGroups: ["rbac.authorization.k8s.io"]
  resources: ["roles", "rolebindings"]
  verbs: ["get", "list", "create", "delete", "update", "patch", "watch"]
- apiGroups: ["*"]
  resources: ["namespaces", "services", "events", "configmaps", "secrets", "pods", "nodes", "endpoints", "persistentvolumes", "persistentvolumeclaims"]
  verbs: ["get", "list", "create", "delete", "update", "patch", "watch"]
- apiGroups: ["*"]
  resources: ["pods/status"]
  verbs: ["update"]
- apiGroups: ["*"]
  resources: ["leases"]
  verbs: ["get", "create","update"]
- apiGroups: ["*"]
  resources: ["nodes/status"]
  verbs: ["update"]
- apiGroups: ["scheduling.k8s.io"]
  resources: ["priorityclasses"]
  verbs: ["get", "list", "watch"]
- apiGroups: [ "apps" ]
  resources: [ "deployments", "statefulsets" ]
  verbs: [ "create", "update", "get", "list", "watch", "delete" ]
- apiGroups: [ "certificates.k8s.io" ]
  resources: [ "certificatesigningrequests" ]
  verbs: [ "create", "update", "get", "list", "watch", "delete" ]
- apiGroups: [ "certificates.k8s.io" ]
  resources: [ "certificatesigningrequests/approval" ]
  verbs: [ "update" ]
- apiGroups: [ "certificates.k8s.io" ]
  resources: [ "signers" ]
  resourceNames: ["kubernetes.io/kubelet-serving"]
  verbs: [ "sign", "approve" ]
- apiGroups: [ "scheduling.eas.alicloud.com" ]
  resources: [ "*" ]
  verbs: [ "*" ]
- apiGroups: [ "networking.eas.alicloud.com" ]
  resources: [ "*" ]
  verbs: [ "*" ]
- apiGroups: [""]
  resources: [ "serviceaccounts" ]
  verbs: [ "create", "update", "get", "delete" ]
- apiGroups: ["rbac.authorization.k8s.io"]
  resources: [ "clusterrolebindings" ]
  verbs: [ "create", "update", "get", "delete" ]
- apiGroups: [ "" ]
  resources: [ "pods/log" ]
  verbs: [ "get" ]
- apiGroups: [ "" ]
  resources: [ "pods/exec" ]
  verbs: [ "create" ]
- apiGroups: ["eas.alibaba-inc.k8s.io"]
  resources: ["tenantresources"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["scheduling.koordinator.sh"]
  resources: ["logicalresourcenodes"]
  verbs:  ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["nodes/proxy"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["daemonsets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["scheduling.sigs.k8s.io"]
  resources: ["elasticquotas"]
  verbs: ["get", "list", "watch", "update"]
- apiGroups: ["apiextensions.k8s.io"]
  resources: ["customresourcedefinitions"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["scheduling.koordinator.sh"]
  resources: ["jobnetworktopologies","clusternetworktopologies"]
  verbs:  ["get", "list", "watch", "create", "update", "delete", "patch"]
- apiGroups: ["scheduling.x-k8s.io"]
  resources: ["queueunits"]
  verbs: ["get", "list", "watch", "create", "update", "delete", "patch"]
- apiGroups: ["scheduling.koordinator.sh"]
  resources: ["logicalresourcenodes", "logicalresourcenodes/status"]
  verbs: ["create", "delete", "get", "list", "patch", "update", "watch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: eas-cluster-controller-binding
  labels:
{{ include "cluster-controller.labels" . | indent 4 }}
subjects:
- kind: ServiceAccount
  name: eas-cluster
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name: eas-cluster-controller-role
  apiGroup: rbac.authorization.k8s.io
