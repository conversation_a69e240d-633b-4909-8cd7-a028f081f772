apiVersion: core.oam.dev/v1alpha2
kind: ApplicationConfiguration
metadata:
  name: deploy-app-package
spec:
  components:
    - revisionName: "ABM_HELM|pai-dsw-operator@${DswOperatorChartMainVersion}@init|${DswRealVersion}"
      scopes:
        - scopeRef:
            apiVersion: flyadmin.alibaba.com/v1alpha1
            kind: Namespace
            name: dsw-system
      dataInputs:
        - valueFrom:
            dataOutputName: "Global.kubeconfig"
          toFieldPaths:
            - spec.base64Kubeconfig
        - valueFrom:
            dataOutputName: "Global.appConfig"
          deepSet: true
          toFieldPaths:
            - spec.values.appConfig
        - valueFrom:
            dataOutputName: "Global.deployConfig"
          deepSet: true
          toFieldPaths:
            - spec.values.deployConfig
        - defaultValue: false
          deepSet: true
          toFieldPaths:
            - spec.values.deployConfig.deployWorkload
        - defaultValue: ["files/*"]
          deepSet: true
          toFieldPaths:
            - spec.values.deployConfig.useFiles
      parameterValues:
        - name: name
          value: "pai-dsw-operator"
          toFieldPaths:
            - spec.name
        - name: applyCrd
          value: true # 可选, 扩展安装和升级CRD的参数, 开启这个有风险，crd不可回滚， 对应kubectl apply -f ./crds 命令 (helm chart crds目录)
          toFieldPaths:
            - spec.applyCrd
    - revisionName: "ABM_HELM|pai-dsw-operator@${DswOperatorChartMainVersion}@shard1|${DswRealVersion}"
      scopes:
        - scopeRef:
            apiVersion: flyadmin.alibaba.com/v1alpha1
            kind: Namespace
            name: dsw-system
      dataInputs:
        - valueFrom:
            dataOutputName: "Global.kubeconfig"
          toFieldPaths:
            - spec.base64Kubeconfig
        - valueFrom:
            dataOutputName: "Global.appConfig"
          deepSet: true
          toFieldPaths:
            - spec.values.appConfig
        - valueFrom:
            dataOutputName: "Global.deployConfig"
          deepSet: true
          toFieldPaths:
            - spec.values.deployConfig
        - valueFrom:
            dataOutputName: "Global.ABM_APP_INSTANCE_ID"
          deepSet: true
          toFieldPaths:
            - spec.values.appConfig.instgo.appName
        - defaultValue: []
          toFieldPaths:
            - spec.values.deployConfig.useFiles
        - defaultValue: true
          toFieldPaths:
            - spec.values.deployConfig.deployWorkload
        - defaultValue: "{{ \"$image1Tag\" | default(Global.deployConfig.dswOperatorImageTag, true) }}"
          deepSet: true
          toFieldPaths:
            - spec.values.deployConfig.dswOperatorImageTag
      parameterValues:
        - name: name
          value: "pai-dsw-operator-shard1"
          toFieldPaths:
            - spec.name
        - name: values
          toFieldPaths:
            - spec.values
          value:
            shardName: "shard1"
      traits:
        - name: write.skyline.oam.apps.abm.io/v1beta1 # for skyline
          runtime: pre
          spec:
            product: "{{ Global.ABM_APP_PRODUCT }}" # 固定，不需要修改
            appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"
        - name: k8s.namespace.create.trait.abm.io/v1beta1
          runtime: pre
          version: "1.0.0"
          spec:
            kubeconfig: "{{ Global.kubeconfig }}" # 固定写法
            name: "dsw" # 填写需要创建的ns的名称，多个用逗号分开
    - revisionName: "ABM_HELM|pai-dsw-operator@${DswOperatorChartMainVersion}@shard2|${DswRealVersion}"
      scopes:
        - scopeRef:
            apiVersion: flyadmin.alibaba.com/v1alpha1
            kind: Namespace
            name: dsw-system
      dataInputs:
        # bool类型的值一定要通过此处定义，并在parameterValues部分显示设置key:""
        - valueFrom:
            dataOutputName: "Global.kubeconfig"
          toFieldPaths:
            - spec.base64Kubeconfig
        - valueFrom:
            dataOutputName: "Global.appConfig"
          deepSet: true
          toFieldPaths:
            - spec.values.appConfig
        - valueFrom:
            dataOutputName: "Global.deployConfig"
          deepSet: true
          toFieldPaths:
            - spec.values.deployConfig
        - valueFrom:
            dataOutputName: "Global.ABM_APP_INSTANCE_ID"
          deepSet: true
          toFieldPaths:
            - spec.values.appConfig.instgo.appName
        - defaultValue: []
          toFieldPaths:
            - spec.values.deployConfig.useFiles
        - defaultValue: true
          toFieldPaths:
            - spec.values.deployConfig.deployWorkload
        - defaultValue: "{{ \"$image1Tag\" | default(Global.deployConfig.dswOperatorImageTag, true) }}"
          deepSet: true
          toFieldPaths:
            - spec.values.deployConfig.dswOperatorImageTag
      parameterValues:
        - name: name
          value: "pai-dsw-operator-shard2"
          toFieldPaths:
            - spec.name
        - name: values
          toFieldPaths:
            - spec.values
          value:
            shardName: "shard2"
      traits:
        - name: write.skyline.oam.apps.abm.io/v1beta1 # for skyline
          runtime: pre
          spec:
            product: "{{ Global.ABM_APP_PRODUCT }}" # 固定，不需要修改
            appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"
    - revisionName: "ABM_HELM|pai-rollout@${helmChartRolloutMainVersion}|${RolloutRealVersion}"
      dataInputs:
        - valueFrom:
            dataOutputName: "Global.kubeconfig"
          toFieldPaths:
            - spec.base64Kubeconfig
        - valueFrom:
            dataOutputName: "Global.deployConfig.rollouts"
          toFieldPaths:
            - spec.values.rollouts
      scopes:
        - scopeRef:
            apiVersion: flyadmin.alibaba.com/v1alpha1
            kind: Cluster
            name: "{{ Global.clusterId }}"
        - scopeRef:
            apiVersion: flyadmin.alibaba.com/v1alpha1
            kind: Namespace
            name: "pai-gray-system"
        - scopeRef:
            apiVersion: flyadmin.alibaba.com/v1alpha1
            kind: Kubeconfig
            name: "{{ Global.kubeconfig }}"
      parameterValues:
        - name: name
          value: "pai-dsw-rollout"
          toFieldPaths:
            - spec.name
        - name: values
          value:
            rollouts: ""
            grayModule: "{{ Global.deployConfig.grayModule }}"
            targetLabel: "{{ Global.deployConfig[Global.shardName].grayReleaseConfig.grayLabelValue }}"
            sourceLabel: "{{ \"A\" if Global.deployConfig[Global.shardName].grayReleaseConfig.grayLabelValue == \"B\" else \"B\" }}"
            rolloutPlan: "{{ Global.deployConfig.abmRolloutPlan }}"
            deployTime: "{{ Global.ABM_APP_INSTANCE_LATEST_UPDATE_TIME }}"
          toFieldPaths:
            - spec.values
    - revisionName: SCRIPT|pai-dsw-operator-status|_
      scopes:
        - scopeRef:
            apiVersion: apps.abm.io/v1
            kind: Namespace
            name: "dsw-system" # 作为一个标识，可随意填写标识，不同 Namespace 的 SCRIPT 探测是隔离的
      dataInputs: # kubeconfig 来源 (base64)
        - valueFrom:
            dataOutputName: "Global.kubeconfig"
          toFieldPaths:
            - spec.base64Kubeconfig
      parameterValues:
        - name: scriptName
          value: "generic-resource/v2"  # 固定为该名称，该名称为中台提供
          toFieldPaths:
            - "spec.scriptName"  # 固定，不要修改
        - name: options
          value:
            endStatus: "RUNNING"  # 当前状态是否需要持续获取 (COMPLETED: 仅 CD 过程中, RUNNING: 持续获取)
            resources: # 你希望检查的资源列表 (必须全部满足)
              - kind: deployments
                namespace: "dsw-system"  # 资源归属 Namespace
                resourceOptions: # 可选
                  checkZeroReplicas: true # 当 true 时，如果 replicas 是 0，会认为检测不通过；
                includes:
                  - "pai-dsw-operator-controller{{ '-' + Global.shardName if Global.shardName != 'shard1' else \"\" }}"
          toFieldPaths:
            - "spec.options"  # 固定不要动

  dynamicWorkflows:
    matchers: # 匹配
      createAppInstance:
        matchableWorkflows: [create]
        defaultWorkflow: create
      updateAppInstance:
        matchableWorkflows: [grayUpgrade, switch, directDeploy]
        defaultWorkflow: grayUpgrade
      rollbackAppInstance:
        matchableWorkflows: [switch, directDeploy]
        defaultWorkflow: switch
    workflows:
      - name: directDeploy
        workflow:
          steps:
            - type: read.tags.rms.apps.abm.io/v1beta1
              name: read-shard-name
              stage: post-render
              outputs:
                - name: shardName
                  valueFrom: shardName
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 从全局变量中获取应用实例ID
                product: "{{ Global.ABM_APP_PRODUCT }}" # 从全局变量中获取产品名称
                tagTraits: # 定义要从 RMS 读取的标签列表
                  - tagName: primary
                    contextKey: shardName
            - type: apply-components
              name: pai-dsw-operator-init
              properties:
                components:
                  - name: pai-dsw-operator@${DswOperatorChartMainVersion}@init
                    type: ABM_HELM
            - type: apply-components
              name: pai-dsw-operator
              inputs:
                - from: shardName
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-dsw-operator@${DswOperatorChartMainVersion}@shard2
                    type: ABM_HELM
                    condition: "Global.shardName=='shard2'"
                  - name: pai-dsw-operator@${DswOperatorChartMainVersion}@shard1
                    type: ABM_HELM
                    condition: "Global.shardName=='shard1'"
            - type: apply-components
              name: pai-dsw-operator-status
              properties:
                components:
                  - name: pai-dsw-operator-status
                    type: SCRIPT
            - type: apply-components
              name: pai-rollout-init
              inputs:
                - from: shardName
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-rollout@${helmChartRolloutMainVersion}
                    type: ABM_HELM
                rollout:
                  type: partition
                  properties:
                    partition: 0
                    name: rolloutPartition
            - type: delete.tags.rms.apps.abm.io/v1beta1 # 使用删除标签的类型
              name: delete-rollout-tag
              stage: post-render
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"
                product: "{{ Global.ABM_APP_PRODUCT }}"
                tags:
                  - rollout

      - name: switch
        workflow:
          steps:
            - type: read.tags.rms.apps.abm.io/v1beta1
              name: read-shard-name
              stage: post-render
              outputs:
                - name: primary
                  valueFrom: primary
                - name: secondary
                  valueFrom: secondary
                - name: rollout
                  valueFrom: rollout
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 从全局变量中获取应用实例ID
                product: "{{ Global.ABM_APP_PRODUCT }}" # 从全局变量中获取产品名称
                tagTraits: # 定义要从 RMS 读取的标签列表
                  - tagName: primary
                    contextKey: primary
                  - tagName: secondary
                    contextKey: secondary
                  - tagName: rollout
                    contextKey: rollout
            - type: add.tags.rms.apps.abm.io/v1beta1
              name: switch-shard-tag
              stage: post-render
              inputs:
                - from: rollout
                  parameterKey: parameters.Global.rollout
                - from: secondary
                  parameterKey: tagMap.primary
                - from: primary
                  parameterKey: tagMap.secondary
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 固定
                product: "{{ Global.ABM_APP_PRODUCT }}" # 固定
                tags: []
                skipCondition: "Global.rollout != ''"
            - type: read.tags.rms.apps.abm.io/v1beta1
              name: read-shard-name-again
              stage: post-render
              outputs:
                - name: primaryNew
                  valueFrom: primary
                - name: secondaryNew
                  valueFrom: secondary
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 从全局变量中获取应用实例ID
                product: "{{ Global.ABM_APP_PRODUCT }}" # 从全局变量中获取产品名称
                tagTraits: # 定义要从 RMS 读取的标签列表
                  - tagName: primary
                    contextKey: primary
                  - tagName: secondary
                    contextKey: secondary
            - type: apply-components
              name: pai-dsw-rollout-step0-rollback
              inputs:
                - from: secondaryNew
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-rollout@${helmChartRolloutMainVersion}
                    type: ABM_HELM
                rollout:
                  type: partition
                  properties:
                    partition: 3
                    name: rolloutPartition
            - type: delete.tags.rms.apps.abm.io/v1beta1 # 使用删除标签的类型
              name: delete-rollout-tag
              stage: post-render
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"
                product: "{{ Global.ABM_APP_PRODUCT }}"
                tags:
                  - rollout
      - name: create
        workflow:
          steps:
            - type: add.tags.rms.apps.abm.io/v1beta1
              name: addTags
              stage: post-render
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 固定
                product: "{{ Global.ABM_APP_PRODUCT }}" # 固定
                tags: # 要打的tag
                  - name: secondary
                    value: "shard2"
                  - name: primary
                    value: "shard1"
            - type: read.tags.rms.apps.abm.io/v1beta1
              name: read-shard-tag
              stage: post-render
              outputs:
                - name: shardName
                  valueFrom: shardName
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 从全局变量中获取应用实例ID
                product: "{{ Global.ABM_APP_PRODUCT }}" # 从全局变量中获取产品名称
                tagTraits: # 定义要从 RMS 读取的标签列表
                  - tagName: primary
                    contextKey: shardName
            - type: apply-components
              name: pai-dsw-operator-init
              properties:
                components:
                  - name: pai-dsw-operator@${DswOperatorChartMainVersion}@init
                    type: ABM_HELM
            - type: apply-components
              name: pai-dsw-operator
              properties:
                components:
                  - name: pai-dsw-operator@${DswOperatorChartMainVersion}@shard1
                    type: ABM_HELM
                  - name: pai-dsw-operator@${DswOperatorChartMainVersion}@shard2
                    type: ABM_HELM
            - type: apply-components
              name: pai-dsw-operator-status
              inputs:
                - from: shardName
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-dsw-operator-status
                    type: SCRIPT
            - type: apply-components
              name: pai-rollout-init
              properties:
                components:
                  - name: pai-rollout@${helmChartRolloutMainVersion}
                    type: ABM_HELM
                rollout:
                  type: partition
                  properties:
                    partition: 0
                    name: rolloutPartition
      - name: grayUpgrade
        workflow:
          steps:
            - type: read.tags.rms.apps.abm.io/v1beta1
              name: read-shard-tag
              stage: post-render
              outputs:
              - name: shardName
                valueFrom: shardName
              - name: primary
                valueFrom: primary
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 从全局变量中获取应用实例ID
                product: "{{ Global.ABM_APP_PRODUCT }}" # 从全局变量中获取产品名称
                tagTraits: # 定义要从 RMS 读取的标签列表
                  - tagName: secondary
                    contextKey: shardName
                  - tagName: primary
                    contextKey: primary
            - type: add.tags.rms.apps.abm.io/v1beta1
              name: rollout-0
              stage: post-render
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 固定
                product: "{{ Global.ABM_APP_PRODUCT }}" # 固定
                tags:
                  - name: rollout
                    value: "0%"
            - type: apply-components
              name: pai-dsw-operator-init
              properties:
                components:
                  - name: pai-dsw-operator@${DswOperatorChartMainVersion}@init
                    type: ABM_HELM
            - type: apply-components
              name: pai-dsw-rollout-step0
              inputs:
                - from: shardName
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-rollout@${helmChartRolloutMainVersion}
                    type: ABM_HELM
                rollout:
                  type: partition
                  properties:
                    partition: 3
                    name: rolloutPartition
            - type: apply-components
              name: pai-dsw-operator
              inputs:
                - from: shardName
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-dsw-operator@${DswOperatorChartMainVersion}@shard1
                    type: ABM_HELM
                    condition: "Global.shardName=='shard1'"
                  - name: pai-dsw-operator@${DswOperatorChartMainVersion}@shard2
                    type: ABM_HELM
                    condition: "Global.shardName=='shard2'"
            - type: apply-components
              name: pai-dsw-operator-status
              inputs:
                - from: shardName
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-dsw-operator-status
                    type: SCRIPT
            - type: suspend
            - type: apply-components
              name: pai-dsw-rollout-step1
              inputs:
                - from: shardName
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-rollout@${helmChartRolloutMainVersion}
                    type: ABM_HELM
                rollout:
                  type: partition
                  properties:
                    partition: 2
                    name: rolloutPartition
            - type: add.tags.rms.apps.abm.io/v1beta1
              name: rollout-30
              stage: post-render
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 固定
                product: "{{ Global.ABM_APP_PRODUCT }}" # 固定
                tags:
                  - name: rollout
                    value: "30%"
            - type: suspend
            - type: apply-components
              name: pai-dsw-rollout-step2
              inputs:
                - from: shardName
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-rollout@${helmChartRolloutMainVersion}
                    type: ABM_HELM
                rollout:
                  type: partition
                  properties:
                    partition: 1
                    name: rolloutPartition
            - type: add.tags.rms.apps.abm.io/v1beta1
              name: rollout-60
              stage: post-render
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 固定
                product: "{{ Global.ABM_APP_PRODUCT }}" # 固定
                tags:
                  - name: rollout
                    value: "60%"
            - type: suspend
            - type: apply-components
              name: pai-dsw-rollout-step3
              inputs:
                - from: shardName
                  parameterKey: parameters.Global.shardName
              properties:
                components:
                  - name: pai-rollout@${helmChartRolloutMainVersion}
                    type: ABM_HELM
                rollout:
                  type: partition
                  properties:
                    partition: 0
                    name: rolloutPartition
            - type: add.tags.rms.apps.abm.io/v1beta1
              name: rollout-100
              stage: post-render
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 固定
                product: "{{ Global.ABM_APP_PRODUCT }}" # 固定
                tags:
                  - name: rollout
                    value: "100%"
            - type: add.tags.rms.apps.abm.io/v1beta1
              name: update-shard-tag
              stage: post-render
              inputs:
                - from: shardName
                  parameterKey: tagMap.primary
                - from: primary
                  parameterKey: tagMap.secondary
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"  # 固定
                product: "{{ Global.ABM_APP_PRODUCT }}" # 固定
                tags: []
            - type: delete.tags.rms.apps.abm.io/v1beta1 # 使用删除标签的类型
              name: delete-rollout-tag
              stage: post-render
              properties:
                appInstanceId: "{{ Global.ABM_APP_INSTANCE_ID }}"
                product: "{{ Global.ABM_APP_PRODUCT }}"
                tags:
                  - rollout