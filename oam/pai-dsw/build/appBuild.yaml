# /build/appBuild.yaml
# reference: https://aliyuque.antfin.com/abm/arhdov/wyokywthk4721nyz#HrEGc
apiVersion: core.oam.dev/v1alpha2
kind: ApplicationCi
metadata:
  name: build-app-package
spec:
  tasks:
    - name: buildCompChart1  # 自定义构建任务名称，随意写
      type: buildHelmPackage  # 固定，用于构建 Helm 包，可选 buildHelmPackage / buildKustomizePackage
      properties:
        componentName: "pai-dsw-operator"  # 关联组件名称
        componentVersion: "1.0.0"        # 关联组件版本
        gitInfo:
          repo: http://gitlab.alibaba-inc.com/pai-mono/pai-mono.git # 组件仓库地址
          branch: "{{  ABM_APP_DEFINE_GIT_BRANCH }}"  # 组件仓库分支
          rootPath: "charts/pai-dsw-operator"  # 主路径
      outputs:
        - name: DswOperatorChartMainVersion  # 自定义，能够和 application.yaml 中引用的地方保持一致即可
          fieldPath: packageOutput.mainVersion  # 固定
        - name: DswRealVersion
          fieldPath: packageOutput.dateTimeVersion
    - name: buildCompChartRollout  # 自定义构建任务名称，随意写
      type: buildHelmPackage  # 固定，用于构建 Helm 包，可选 buildHelmPackage / buildKustomizePackage
      properties:
        componentName: "pai-rollout"  # 关联组件名称
        componentVersion: "1.0.0"  # 关联组件版本由应用自动生成
        gitInfo:
          repo: http://gitlab.alibaba-inc.com/pai-mono/pai-mono.git  # 组件仓库地址
          branch: "{{  ABM_APP_DEFINE_GIT_BRANCH }}"  # 组件仓库分支
          rootPath: "charts/pai-rollout"  # 主路径
      outputs:
        - name: helmChartRolloutMainVersion  # 自定义，能够和 application.yaml 中引用的地方保持一致即可
          fieldPath: packageOutput.mainVersion  # 固定
        - name: RolloutRealVersion
          fieldPath: packageOutput.dateTimeVersion  # 固定