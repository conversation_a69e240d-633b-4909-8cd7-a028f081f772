generate:
  object:
    enable: true
    path: ./apis

  crd:
    enable: true
    path: ./apis
    defaultConfig:
      name: "{{ .PackageName }}"
      enable: true
      outputPath: "./pkg/{{ .PackageName }}/config/crd/bases"
      maxDescLen: 0
    configs:
      - name: "paiimagejob"
        outputPath: "./pkg/pai-dlc-operator/config/crd/bases"
      - name: "paiquota"
        outputPath: "./pkg/pai-dlc-operator/config/crd/bases"
      - name: "quotanodebinder"
        outputPath: "./pkg/pai-dlc-operator/config/crd/bases"
      - name: "resourcegroup"
        outputPath: "./pkg/pai-dlc-operator/config/crd/bases"
      - name: "lrnhint"
        outputPath: "./pkg/pai-dlc-operator/config/crd/bases"
      - name: "pai-slurm"
        outputPath: "./pkg/pai-slurm-operator/config/crd/bases"
      - name: "pai-migration-controller"
        enable: false
      - name: "pai-preview-controller"
        enable: false
      - name: "pai-config-operator"
        maxDescLen: 999

  rbac:
    enable: true
    path: ./pkg
    defaultConfig:
      name: "{{ .PackageName }}"
      enable: true
      outputPath: "./pkg/{{ .PackageName }}/config/rbac"
      roleName: "{{ .PackageName }}-manager-role"
    configs:
      - name: "dsw-controllers"
        roleName: "dsw-manager-role"
      - name: "pai-dlc-operator"
        roleName: "manager-role"
      - name: "pai-credential-operator"
        roleName: "manager-role"
      - name: "pai-notification"
        roleName: "manager-role"
      - name: "pai-training-operator"
        roleName: "manager-role"

  webhook:
    enable: true
    path: ./pkg

copy:
  enable: true
  path: ./pkg
  defaultConfig:
    name: "{{ .PackageName }}"
    enableCrdCopy: true
    enableRbacCopy: false
    enableRbacNamespaceReplace: true
    rbacNamespace: "{{ .PackageName }}-system"
    rbacOutputPath: "./charts/{{ .PackageName }}/templates/rbac.yaml"
    crdOutputPath: "./charts/{{ .PackageName }}/crds/crds.yaml"
  configs:
    - name: "dsw-controllers"
      enableRbacCopy: true
      rbacNamespace: "dsw-system"
      rbacOutputPath: "./charts/pai-dsw-operator/files/rbac.yaml"
      crdOutputPath: "./charts/pai-dsw-operator/crds/crds.yaml"
    - name: "pai-dlc-operator"
      enableRbacCopy: true
      rbacNamespace: "pai-dlc-system"
      rbacOutputPath: "./charts/pai-dlc-operator/files/rbac.yaml"
    - name: "pai-slurm-operator"
      enableRbacCopy: true
      enableCrdCopy: false
      rbacNamespace: "pai-dlc-system"
    - name: "datasource-operator"
      crdOutputPath: "./charts/pai-datasource-operator/crds/crds.yaml"
    - name: "instance-forwarder-operator"
      enableCrdCopy: false
    - name: "pai-credential-operator"
      enableCrdCopy: false
    - name: "pai-notification"
      enableCrdCopy: false
    - name: "pai-training-operator"
      enableCrdCopy: false
    - name: "mono-builder"
      enableCrdCopy: false
