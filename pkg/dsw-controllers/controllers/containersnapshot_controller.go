// TODO: add gosec G601 linter after bump go version to 1.22
// #nosec G601

package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	dswv1 "pai-mono/apis/dsw-controllers/v1"
	"pai-mono/pkg/dsw-controllers/configs"
	"pai-mono/pkg/dsw-controllers/utils"
	"pai-mono/pkg/pai-dlc-operator/common/helper/cached"
	"pai-mono/pkg/pai-rec-service/pkg/constants"
	"pai-mono/pkg/util/grayrelease"
	stringUtil "pai-mono/pkg/util/string"
	"strconv"
	"time"

	ctrlutil "sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/source"

	"sigs.k8s.io/controller-runtime/pkg/controller"

	"github.com/go-logr/logr"
	"github.com/petermattis/goid"
	pccv1 "gitlab.alibaba-inc.com/serverlessinfra/api/pkg/apis/persistentcontainerclaim/v1"
	corev1 "k8s.io/api/core/v1"
	apierrs "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	k8sretry "k8s.io/client-go/util/retry"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	containerSnapshotFinalizerName = "containersnapshot.finalizers.dsw.alibaba.com"
)

// ContainerSnapshotReconciler reconciles a ContainerSnapshot object
type ContainerSnapshotReconciler struct {
	client.Client
	Log           logr.Logger
	Scheme        *runtime.Scheme
	APIReader     client.Reader
	EventRecorder record.EventRecorder
	Config        *configs.Config
}

// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=containersnapshots,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=containersnapshots/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=core,resources=pods,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=pods/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=runtime.alibabacloud.com,resources=persistentcontainerclaims,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=runtime.alibabacloud.com,resources=persistentcontainerclaims/status,verbs=get

// Reconcile defines the entrypoint of the controller
func (r *ContainerSnapshotReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := r.Log.WithValues("containersnapshot", req.NamespacedName, "goId", goid.Get())

	snapshot := &dswv1.ContainerSnapshot{}
	err := r.Get(ctx, req.NamespacedName, snapshot)
	if err != nil {
		if apierrs.IsNotFound(err) {
			logger.Info("ContainerSnapshot not found")
			return ctrl.Result{}, nil
		}
		logger.Error(err, "Failed to get containerSnapshot")
		return ctrl.Result{}, err
	}

	logger = utils.GetLogger(logger, snapshot)

	if snapshot.IsBeingDeleted() {
		if result, err := r.handleFinalizer(snapshot); result.Requeue || err != nil {
			return result, err
		} else {
			r.EventRecorder.Event(snapshot, corev1.EventTypeNormal, "Deleted", "Object finalizer is deleted")
			return ctrl.Result{}, nil
		}
	}
	// add finalizer if not exists
	if !snapshot.HasFinalizer(containerSnapshotFinalizerName) {
		snapshot.AddFinalizer(containerSnapshotFinalizerName)
		if err := r.Update(ctx, snapshot); err != nil {
			logger.Error(err, "failed to add finalizer for containerSnapshot")
			return ctrl.Result{}, err
		}
	}

	if snapshot.Status.Phase == dswv1.ImageStatusSaved || snapshot.Status.Phase == dswv1.ImageStatusFailed {
		logger.Info("ContainerSnapshot is already saved or failed")
		return ctrl.Result{}, nil
	}

	if result, err := r.reconcileCopyTargetSecret(snapshot); err != nil || result.Requeue {
		return result, err
	}

	// get or create pcc cr for registry image
	pccToRegistry := &pccv1.PersistentContainerClaim{}
	if err := r.Get(ctx, types.NamespacedName{Name: snapshot.Name, Namespace: snapshot.Namespace}, pccToRegistry); err != nil {
		if apierrs.IsNotFound(err) {
			err = r.APIReader.Get(ctx, types.NamespacedName{Name: snapshot.Name, Namespace: snapshot.Namespace}, pccToRegistry)
			if err != nil {
				if apierrs.IsNotFound(err) {
					result, err := r.preparePccToRegistry(snapshot, pccToRegistry)
					if result.Requeue || err != nil {
						return result, err
					}
					if err := ctrlutil.SetControllerReference(snapshot, pccToRegistry, r.Scheme); err != nil {
						return ctrl.Result{}, err
					}
					if err := r.Create(ctx, pccToRegistry); err != nil {
						logger.Error(err, "failed to create pcc")
						return ctrl.Result{}, err
					}
					status := dswv1.ContainerSnapshotStatus{
						Phase: dswv1.ImageStatusInitialized,
					}
					err = r.updateStatus(snapshot, &status)
					if err != nil {
						logger.Error(err, "failed to update status of containerSnapshot")
						return ctrl.Result{}, err
					}
					return utils.Requeue(utils.ReconcileInterval)
				} else {
					logger.Error(err, "failed to get pcc")
					return ctrl.Result{}, err
				}
			}
		} else {
			logger.Error(err, "failed to get pcc")
			return ctrl.Result{}, err
		}
	}
	// watch status of pcc object, update status of containerSnapshot object
	var snapshotPhase dswv1.ContainerSnapshotPhase
	message := stringUtil.EmptyString

	switch pccToRegistry.Status.Phase {
	case pccv1.ClaimSucceeded:
		snapshotPhase = dswv1.ImageStatusSaved
		message = stringUtil.EmptyString
	case pccv1.ClaimFailed:
		snapshotPhase = dswv1.ImageStatusFailed
		message = utils.GetPccErrorMessage(pccToRegistry)
	case pccv1.ClaimAvailable:
		// PCC job is in processing
		snapshotPhase, message = utils.GetSnapshotStatus(pccToRegistry)
	default:
		logger.Info("unknown pcc phase", "phase", pccToRegistry.Status.Phase)
	}

	if snapshotPhase != stringUtil.EmptyString {
		status := dswv1.ContainerSnapshotStatus{
			Phase:      snapshotPhase,
			Message:    message,
			Conditions: r.getSnapshotConditions(pccToRegistry.Status.Conditions),
		}
		err = r.updateStatus(snapshot, &status)
		if err != nil {
			logger.Error(err, "failed to update status of containerSnapshot")
			return ctrl.Result{}, err
		}
	}
	return ctrl.Result{}, nil
}

func (r *ContainerSnapshotReconciler) getSnapshotConditions(pccConditions []pccv1.PersistentContainerClaimCondition) []dswv1.ContainerSnapshotCondition {
	var conditions []dswv1.ContainerSnapshotCondition
	for _, condition := range pccConditions {
		cond := dswv1.ContainerSnapshotCondition{
			Type:               dswv1.ContainerSnapshotConditionType(condition.Type),
			Status:             condition.Status,
			Reason:             condition.Reason,
			Message:            condition.Message,
			LastTransitionTime: condition.LastTransitionTime,
		}
		conditions = append(conditions, cond)
	}
	return conditions
}

func (r *ContainerSnapshotReconciler) convertRoleArn(snapshot *dswv1.ContainerSnapshot) (string, error) {
	var roleArn string
	if snapshot.Spec.RoleArn != constants.EmptyString {
		roleArn = snapshot.Spec.RoleArn
	} else if snapshot.Spec.RoleChainCredential != nil && len(snapshot.Spec.RoleChainCredential.Roles) > 0 {
		rolesBytes, err := json.Marshal(snapshot.Spec.RoleChainCredential.Roles)
		if err != nil {
			return constants.EmptyString, err
		}
		roleArn = string(rolesBytes)
	}

	return roleArn, nil
}

func (r *ContainerSnapshotReconciler) reconcileCopyTargetSecret(snapshot *dswv1.ContainerSnapshot) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, snapshot)

	secretName := utils.GetContainerSnapshotSecretName(snapshot.Name)
	foundSecret := &corev1.Secret{}
	err := r.Get(context.TODO(), types.NamespacedName{Name: secretName, Namespace: snapshot.Namespace}, foundSecret)
	if err != nil {
		if apierrs.IsNotFound(err) {
			secretData, err := r.getSecretData(snapshot)
			if err != nil {
				logger.Error(err, "Failed to get user temp auth")
				return ctrl.Result{}, err
			}
			expiration := time.Now().Unix() + utils.ACRSecretRenewIntervalSeconds

			secret := &corev1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      secretName,
					Namespace: snapshot.Namespace,
					Annotations: map[string]string{
						utils.ACRAnnotationRenewable:  stringUtil.TrueString,
						utils.ACRAnnotationExpiration: strconv.FormatInt(expiration, 10),
					},
				},
				Data: map[string][]byte{
					corev1.DockerConfigJsonKey: []byte(secretData),
				},
				Type: corev1.SecretTypeDockerConfigJson,
			}
			if err := ctrlutil.SetControllerReference(snapshot, secret, r.Scheme); err != nil {
				return ctrl.Result{}, err
			}
			err = r.Create(context.TODO(), secret)
			if err != nil {
				logger.Error(err, "failed to create secret for target acr.")
				return ctrl.Result{}, err
			}
			return ctrl.Result{}, nil
		} else {
			logger.Error(err, "failed to get secret for target acr.")
			return ctrl.Result{}, err
		}
	}
	if !isExpired(foundSecret) {
		return ctrl.Result{}, nil
	}

	// get Registry auth and update secret
	secretData, err := r.getSecretData(snapshot)
	if err != nil {
		logger.Error(err, "Failed to get user temp auth")
		return ctrl.Result{}, err
	}

	newExpiration := time.Now().Unix() + utils.ACRSecretRenewIntervalSeconds
	foundSecret.Annotations[utils.ACRAnnotationExpiration] = strconv.FormatInt(newExpiration, 10)
	foundSecret.Data[corev1.DockerConfigJsonKey] = []byte(secretData)

	logger.Info("Renew ACR-EE ImagePullSecret")

	result, err := ctrl.Result{}, r.Update(context.TODO(), foundSecret)
	if err != nil {
		logger.Error(err, "failed to update secret for target acr.")
		return result, err
	}
	return result, err
}

func (r *ContainerSnapshotReconciler) getSecretData(snapshot *dswv1.ContainerSnapshot) (string, error) {
	logger := utils.GetLogger(r.Log, snapshot)
	var roleArn, acrInstanceOwner string
	var acrInstanceIDs []string
	var err error
	if utils.IsContainerSnapshotToPaiRegistry(snapshot) {
		roleArn = utils.GetDefaultRoleArn(r.Config.ResourceAccountId, r.Config.DefaultRole)
		acrInstanceOwner = r.Config.ResourceAccountId
		if r.Config.AcrInstanceId != stringUtil.EmptyString {
			acrInstanceIDs = []string{r.Config.AcrInstanceId}
		}
	} else {
		roleArn, err = r.convertRoleArn(snapshot)
		if err != nil {
			logger.Error(err, "failed to convert roleArn")
			return stringUtil.EmptyString, err
		}
		acrInstanceOwner = snapshot.Spec.UserId
		acrInsts, err := GetAcrEEInstancesFromImages([]string{snapshot.Spec.ImageUrl}, snapshot.Spec.UserId, roleArn, r.Config)
		if err != nil {
			logger.Error(err, "Failed to calc ACR Instance from imageUrl", "imageUrl", snapshot.Spec.ImageUrl)
			return stringUtil.EmptyString, err
		}
		if acrInsts != nil && len(acrInsts) == 1 {
			acrInstanceIDs = []string{acrInsts[0]}
		}
	}

	var tempAuthData []byte

	if len(acrInstanceIDs) == 1 {
		logger.Info("Get user temp auth token for acr ee edition", "acrInstance", acrInstanceIDs[0])
		manager, err := cached.NewCachedAcrManager(
			acrInstanceOwner,
			roleArn,
			r.Config.Region,
			r.Config.EndpointNetwork,
			r.Config.EndpointType,
			r.Config.ServiceAccountAccessKeyId,
			r.Config.ServiceAccountAccessKeySecret,
			r.Config.StsEndpoint)
		if err != nil {
			return stringUtil.EmptyString, err
		}
		return manager.GeneratePullSecret(acrInstanceIDs)
	} else {
		logger.Info("Get user temp auth token for acr personal edition")
		tempAuthData, err = utils.GetUserTempAuthToken(snapshot.Spec.UserId, roleArn, *r.Config)
		if err != nil {
			logger.Error(err, "Failed to get TempAuthToken from acr personal edition")
			return stringUtil.EmptyString, err
		}
		secretData := generateImagePullSecret(r.Config.Region, string(tempAuthData))
		return secretData, nil
	}
}

func (r *ContainerSnapshotReconciler) preparePccToRegistry(snapshot *dswv1.ContainerSnapshot, pccToRegistry *pccv1.PersistentContainerClaim) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, snapshot)
	runningPod, containerName, err := r.fetchOrCreatePodContainer(snapshot)
	if err != nil {
		logger.Error(err, "Failed to fetch or create pod container")
		return ctrl.Result{}, err
	}

	pccToRegistry.ObjectMeta = metav1.ObjectMeta{
		Name:      snapshot.Name,
		Namespace: snapshot.Namespace,
	}

	pccToRegistry.Spec = pccv1.PersistentContainerClaimSpec{
		CopySource: &pccv1.CopySource{
			PodContainer: &pccv1.PodContainerObject{
				NamespacedObject: pccv1.NamespacedObject{
					Namespace: runningPod.Namespace,
					Name:      runningPod.Name,
				},
				ContainerName: containerName,
			},
		},
		CopyTarget: pccv1.CopyTarget{
			Registry: &pccv1.RegistryImage{
				Image: snapshot.Spec.ImageUrl,
				Secret: &corev1.LocalObjectReference{
					Name: utils.GetContainerSnapshotSecretName(snapshot.Name),
				},
			},
		},
	}
	return ctrl.Result{}, nil
}

// Fetch or create pod container
// WIP. Create a new pod container based on a pcc object.
func (r *ContainerSnapshotReconciler) fetchOrCreatePodContainer(snapshot *dswv1.ContainerSnapshot) (*corev1.Pod, string, error) {
	logger := utils.GetLogger(r.Log, snapshot)

	if snapshot.Spec.PodContainer == nil || snapshot.Spec.PodContainer.ContainerName == stringUtil.EmptyString {
		return nil, constants.EmptyString, fmt.Errorf("pod container is not specified")
	}
	pod := &corev1.Pod{}
	if snapshot.Spec.PodContainer.NameSelector != nil && snapshot.Spec.PodContainer.NameSelector.Name != stringUtil.EmptyString &&
		snapshot.Spec.PodContainer.NameSelector.Namespace != stringUtil.EmptyString {

		if err := r.Get(context.TODO(), types.NamespacedName{Name: snapshot.Spec.PodContainer.NameSelector.Name,
			Namespace: snapshot.Spec.PodContainer.NameSelector.Namespace}, pod); err != nil {
			if apierrs.IsNotFound(err) {
				return nil, constants.EmptyString, fmt.Errorf("pod not found")
			} else {
				return nil, constants.EmptyString, err
			}
		}
	} else if snapshot.Spec.PodContainer.PodLabelSelector != nil {
		selector, err := metav1.LabelSelectorAsSelector(snapshot.Spec.PodContainer.PodLabelSelector)
		if err != nil {
			logger.Error(err, "Failed to construct label selector requirement.", "PodLabelSelector",
				snapshot.Spec.PodContainer.PodLabelSelector)
			return nil, constants.EmptyString, err
		}

		podList := &corev1.PodList{}
		if err := r.List(context.TODO(), podList, client.MatchingLabelsSelector{Selector: selector}); err != nil {
			logger.Error(err, "Failed to list pods.", "PodLabelSelector", snapshot.Spec.PodContainer.PodLabelSelector)
			return nil, constants.EmptyString, err
		}
		if len(podList.Items) == 0 {
			return nil, constants.EmptyString, fmt.Errorf("pod not found")
		}
		if len(podList.Items) > 1 {
			return nil, constants.EmptyString, fmt.Errorf("more than one pod found")
		}
		pod = &podList.Items[0]
	} else {
		return nil, constants.EmptyString, fmt.Errorf("pod container is not specified with valid selector")
	}

	if pod.Status.Phase != corev1.PodRunning {
		return nil, constants.EmptyString, fmt.Errorf("pod is not running")
	}
	return pod, snapshot.Spec.PodContainer.ContainerName, nil
}

func (r *ContainerSnapshotReconciler) getRunningPod(image *dswv1.Image) (*corev1.Pod, error) {
	namespace := image.Namespace
	name := image.Name
	var podList = &corev1.PodList{}
	if err := r.List(context.TODO(), podList, client.InNamespace(namespace), client.MatchingLabels{
		"deployment": name,
	}); err != nil {
		return nil, err
	}

	funcGetPodStatus := func(pod *corev1.Pod) string {
		status := string(pod.Status.Phase)
		for _, cond := range pod.Status.ContainerStatuses {
			if cond.State.Waiting != nil {
				status = cond.State.Waiting.Reason
				break
			}
		}
		return status
	}

	var runningPod *corev1.Pod
	for _, pod := range podList.Items {
		if funcGetPodStatus(&pod) == string(corev1.PodRunning) {
			runningPod = &pod
			break
		}
	}
	return runningPod, nil
}

func (r *ContainerSnapshotReconciler) updateStatus(snapshot *dswv1.ContainerSnapshot, status *dswv1.ContainerSnapshotStatus) error {
	return k8sretry.RetryOnConflict(k8sretry.DefaultBackoff, func() error {
		foundSnapshot := &dswv1.ContainerSnapshot{}
		if err := r.Get(context.TODO(), types.NamespacedName{Name: snapshot.Name, Namespace: snapshot.Namespace}, foundSnapshot); err != nil {
			if apierrs.IsNotFound(err) {
				return nil
			}
			return err
		}
		if status.PCCName != stringUtil.EmptyString {
			foundSnapshot.Status.PCCName = status.PCCName
		}
		if status.Phase != stringUtil.EmptyString {
			foundSnapshot.Status.Phase = status.Phase
		}
		if status.Message != stringUtil.EmptyString {
			foundSnapshot.Status.Message = status.Message
		}
		if len(status.Conditions) > 0 {
			foundSnapshot.Status.Conditions = status.Conditions
		}
		foundSnapshot.Status.LastUpdateTime = &metav1.Time{Time: time.Now()}

		return r.Status().Update(context.TODO(), foundSnapshot)
	})
}

func (r *ContainerSnapshotReconciler) handleFinalizer(containerSnapshot *dswv1.ContainerSnapshot) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, containerSnapshot)

	if containerSnapshot.HasFinalizer(containerSnapshotFinalizerName) {
		logger.Info("clean up containerSnapshot")
		if result, err := r.deletePcc(containerSnapshot); err != nil || result.Requeue {
			return result, err
		}

		if !utils.IsContainerSnapshotFinalStatus(containerSnapshot) {
			status := dswv1.ContainerSnapshotStatus{
				Phase: dswv1.ContainerSnapshotInterrupted,
			}
			err := r.updateStatus(containerSnapshot, &status)
			if err != nil {
				logger.Error(err, "failed to update status of containerSnapshot")
				return ctrl.Result{}, err
			}
		}
		containerSnapshot.RemoveFinalizer(containerSnapshotFinalizerName)
		return ctrl.Result{}, r.Update(context.Background(), containerSnapshot)
	}
	return ctrl.Result{}, nil
}

func (r *ContainerSnapshotReconciler) deletePcc(containerSnapshot *dswv1.ContainerSnapshot) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, containerSnapshot)

	foundPcc := &pccv1.PersistentContainerClaim{}
	if err := r.Get(context.TODO(), types.NamespacedName{Name: containerSnapshot.Name, Namespace: containerSnapshot.Namespace}, foundPcc); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		}
		logger.Error(err, "failed to get pcc object for deleting")
		return ctrl.Result{}, err
	}
	if err := r.Delete(context.TODO(), foundPcc); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		}
		logger.Error(err, "failed to delete pcc object")
		return ctrl.Result{}, err
	}
	logger.Info("Wait until pcc object is deleted")
	return utils.Requeue(utils.ReconcileInterval)
}

// SetupWithManager setup the controller manager
func (r *ContainerSnapshotReconciler) SetupWithManager(mgr ctrl.Manager) error {
	var controllerBuilder grayrelease.ControllerBuilder
	if r.Config.EnableGrayRelease {
		controllerBuilder = grayrelease.NewWrapperControllerManagedBy(mgr).
			WithExtraOptions(grayrelease.WrapperOptions{})
	} else {
		controllerBuilder = grayrelease.NewStandardControllerManagedBy(mgr)
	}
	return controllerBuilder.
		For(&dswv1.ContainerSnapshot{}).
		Watches(&source.Kind{Type: &pccv1.PersistentContainerClaim{}}, &handler.EnqueueRequestForOwner{
			IsController: true,
			OwnerType:    &dswv1.ContainerSnapshot{},
		}).
		WithOptions(
			controller.Options{
				MaxConcurrentReconciles: r.Config.MaxConcurrentReconciles,
				RecoverPanic:            true,
			},
		).
		Complete(r)
}
