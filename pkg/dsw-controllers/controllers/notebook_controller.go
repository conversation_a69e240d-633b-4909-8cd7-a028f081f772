// TODO: add gosec G601 linter after bump go version to 1.22
// #nosec G601

package controllers

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"pai-mono/pkg/dsw-controllers/features"
	featureUtil "pai-mono/pkg/dsw-controllers/utils/features"
	utilsConstant "pai-mono/pkg/util/constants"
	fgUtil "pai-mono/pkg/util/featuregate"
	"pai-mono/pkg/util/grayrelease"
	ossClient "pai-mono/pkg/util/oss/client"
	"pai-mono/pkg/util/paivnode"

	rmcoreSdk "github.com/alibabacloud-go/rmcore-inc-20221121/client"
	"k8s.io/apimachinery/pkg/util/cache"

	ossSdk "github.com/aliyun/aliyun-oss-go-sdk/oss"

	dsv1 "pai-mono/apis/datasource-operator/v1"
	dswv1 "pai-mono/apis/dsw-controllers/v1"
	pqv1 "pai-mono/apis/paiquota/v1"
	rgv1 "pai-mono/apis/resourcegroup/v1"
	"pai-mono/pkg/datasource-operator/pkg/common"
	aliyunUtils "pai-mono/pkg/dsw-controllers/aliyun/utils"
	"pai-mono/pkg/dsw-controllers/configs"
	"pai-mono/pkg/dsw-controllers/helper"
	"pai-mono/pkg/dsw-controllers/utils"
	cmv1 "pai-mono/pkg/pai-dlc-operator/common"
	"pai-mono/pkg/pai-dlc-operator/common/aliyun/vpc"
	"pai-mono/pkg/pai-dlc-operator/common/helper/cached"
	pvtzutil "pai-mono/pkg/pai-dlc-operator/common/helper/pvtz"
	dlcUtils "pai-mono/pkg/pai-dlc-operator/common/utils"
	persistenceutil "pai-mono/pkg/persistence-agent/storage/manager/plugins/pod"
	"pai-mono/pkg/util/rdma"
	resourceutil "pai-mono/pkg/util/resource"
	"pai-mono/pkg/util/spot"
	stringUtil "pai-mono/pkg/util/string"
	"reflect"
	rt "runtime"
	"strconv"
	"strings"
	"time"

	ecssdk "github.com/aliyun/alibaba-cloud-sdk-go/services/ecs"
	mapset "github.com/deckarep/golang-set"
	spotv1 "gitlab.alibaba-inc.com/pai-dlc/pai-spot-operator/api/v1"
	"gitlab.alibaba-inc.com/pai-mono-third-party/apis/pvtz"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	"pai-mono/pkg/pai-resource-service/config/constants"

	"sigs.k8s.io/controller-runtime/pkg/source"

	rmcoreClient "pai-mono/pkg/util/rmcore/client"

	"github.com/go-logr/logr"
	"github.com/imdario/mergo"
	queuev1 "github.com/kube-queue/api/pkg/apis/scheduling/v1alpha1"
	"github.com/petermattis/goid"
	mtsv1 "gitlab.alibaba-inc.com/PAI/mts/apis/external/v1"
	"golang.org/x/exp/slices"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	networkingv1beta1 "k8s.io/api/networking/v1beta1"
	apierrs "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/tools/record"
	k8sretry "k8s.io/client-go/util/retry"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

const notebookFinalizer = "notebook.finalizers.dsw.alibaba.com"

const defaultContainerPort = 8888
const defaultServingPort = 80
const sidecarContainerPort = 8889
const defaultContainerSSHPort = 22
const defaultServingSSHPort = 22

const proxySidecarPort = 8086

const NASRoot = "/"

const timeLayout = "2006-01-02T15:04:05Z"

const (
	// EciConditionTypeContainerInstanceCreated eci custom condition type: https://help.aliyun.com/zh/eci/user-guide/eci-custom-condition#61de43d047mw3
	EciConditionTypeContainerInstanceCreated = "ContainerInstanceCreated"
)

var notebookStatusCanSaveImage = mapset.NewSet(dswv1.Running)

const (
	EnableIngress                     = "ENABLE_INGRESS"
	DswSidecarResourceCPULimit        = "1"
	DswSidecarResourceMemoryLimit     = "256Mi"
	DswSidecarResourceCPURequest      = "0"
	DswSidecarResourceMemoryRequest   = "0Mi"
	SetupSidecarResourceCPULimit      = "2"
	SetupSidecarResourceMemoryLimit   = "512Mi"
	SetupSidecarResourceCPURequest    = "0"
	SetupSidecarResourceMemoryRequest = "0Mi"
	ProxySidecarResourceCPULimit      = "1"
	ProxySidecarResourceMemoryLimit   = "256Mi"
	ProxySidecarResourceCPURequest    = "0"
	ProxySidecarResourceMemoryRequest = "0Mi"
	ImageIdLabelKey                   = "image-id"
	AlbSuffix                         = "-alb"
	GlooSuffix                        = "-gloo"
	ToolboxScriptEciUpdateHosts       = "/opt/toolbox/eci_update_hosts.sh"
	SetupAcreeHostAliasContainer      = "setup-acree-hostalias"
)

var (
	containerFailReasons = []string{
		"CrashLoopBackOff", "InvalidImageName", "CreateContainerError",
	}

	imagePullFailedReasons = []string{
		"ErrImagePull", "ImagePullBackOff",
	}

	outOfStockPattern = "nostock"
)

var syncResourceOperationCache = cache.NewLRUExpireCache(10000)

// NotebookReconciler reconciles a Notebook object
type NotebookReconciler struct {
	client.Client
	Log               logr.Logger
	Scheme            *runtime.Scheme
	APIReader         client.Reader
	EventRecorder     record.EventRecorder
	Config            *configs.Config
	DataSourceService *DataSourceService
	RmcoreClient      rmcoreClient.Client
}

// +kubebuilder:rbac:groups=forwarder.pai.alibaba-inc.com,resources=instanceforwarders,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=apiextensions.k8s.io,resources=customresourcedefinitions,verbs=get;list
// +kubebuilder:rbac:groups=apps,resources=deployments,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=apps,resources=deployments/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=core,resources=configmaps,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=services,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=services/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=core,resources=pods,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=pods/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=networking.k8s.io,resources=ingresses,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=networking.k8s.io,resources=ingresses/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=notebooks,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=notebooks/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=scheduling.x-k8s.io,resources=queueunits,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=scheduling.x-k8s.io,resources=queueunits/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=runtime.alibabacloud.com,resources=persistentcontainerclaims,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=runtime.alibabacloud.com,resources=persistentcontainerclaims/status,verbs=get
// +kubebuilder:rbac:groups=core,resources=persistentvolumeclaims,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=persistentvolumeclaims/status,verbs=get

// Reconcile as the entrypoint of reconciler
func (r *NotebookReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := r.Log.WithValues("notebook", req.NamespacedName, "goId", goid.Get())

	// Get notebook notebook
	notebook := &dswv1.Notebook{}
	err := r.Get(ctx, req.NamespacedName, notebook)
	if err != nil {
		if apierrs.IsNotFound(err) {
			logger.Info("Not found notebook")
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}

	logger = utils.GetLogger(r.Log, notebook)

	// Get and register feature gate
	baseFg := featureUtil.GetFeatureGate(ctx)
	fg := fgUtil.NewWhiteListFeatureGate(fgUtil.GetFromAnnotation(notebook.Annotations, featureUtil.FeatureGateAnnotationKey), baseFg)
	ctx = featureUtil.WithWhiteListFeatureGate(ctx, fg)

	if notebook.Labels == nil {
		notebook.Labels = make(map[string]string)
	}

	if notebook.IsBeingDeleted() {
		logger.Info("HandleFinalizer for notebook")
		if result, err := r.handleFinalizer(notebook); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}

		r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Deleted", "Object finalizer is deleted")
		return ctrl.Result{}, nil
	}

	if !notebook.HasFinalizer(notebookFinalizer) {
		logger.Info("AddFinalizer for notebook")
		if err := r.addFinalizer(notebook); err != nil {
			r.EventRecorder.Event(notebook, corev1.EventTypeWarning, "Adding finalizer", fmt.Sprintf("Failed to add finalizer: %s", err))
			return ctrl.Result{}, fmt.Errorf("error when adding finalizer: %v", err)
		}
		r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "Added", "Object finalizer is added")
		return utils.Requeue(utils.ReconcileInterval)
	}

	if notebook.Status.Status == dswv1.SaveFailed || notebook.Status.Status == dswv1.Saved {
		// sleep 2s to ensure that the saved and saveFailed status occur before running
		time.Sleep(2 * time.Second)
		if _, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
			Status: dswv1.Running,
		}); err != nil {
			return ctrl.Result{}, err
		}
		return utils.Requeue(utils.ReconcileInterval)
	}

	for _, reconcileFunc := range []func(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error){
		r.reconcileQueueUnit,
		r.reconcileConfigmap,
		r.reconcileUserCommand,
		r.reconcileService,
		r.reconcileDynamicPodLabel,
		r.reconcileDeployment,
		r.reconcileRouteEntryInUserVPC,
		r.reconcileIngress,
		r.reconcilePreemptDetection,
		r.reconcileStatus,
		r.reportNodeError,
		r.reconcileImage,
		r.reconcileAnnotations,
	} {
		if result, err := reconcileFunc(ctx, notebook); utils.HasErrorOrRequeue(result, err) {
			logger.Info("reconcile Notebook CR, has error or need requeue", "err", err, "result", result, "reconcileFunc", rt.FuncForPC(reflect.ValueOf(reconcileFunc).Pointer()).Name())
			return result, err
		}
	}
	return ctrl.Result{}, nil
}

// SetupWithManager setup the controller manager
func (r *NotebookReconciler) SetupWithManager(mgr ctrl.Manager) error {
	if err := mgr.GetFieldIndexer().IndexField(context.Background(), &corev1.Event{}, ".involvedObject.name", func(rawObj client.Object) []string {
		event := rawObj.(*corev1.Event)
		return []string{event.InvolvedObject.Name}
	}); err != nil {
		return err
	}

	var controllerBuilder grayrelease.ControllerBuilder
	if r.Config.EnableGrayRelease {
		controllerBuilder = grayrelease.NewWrapperControllerManagedBy(mgr).
			WithExtraOptions(grayrelease.WrapperOptions{})
	} else {
		controllerBuilder = grayrelease.NewStandardControllerManagedBy(mgr)
	}

	controllerBuilder = controllerBuilder.
		For(&dswv1.Notebook{}).
		Owns(&appsv1.Deployment{}).
		Owns(&corev1.Pod{}).
		Owns(&corev1.Service{}).
		Owns(&dswv1.Image{}).
		Owns(&dswv1.NasVolume{}).
		Owns(&dswv1.Credential{}).
		Watches(&source.Kind{Type: &dswv1.ContainerSnapshot{}}, handler.EnqueueRequestsFromMapFunc(
			func(obj client.Object) []reconcile.Request {
				snapshot := obj.(*dswv1.ContainerSnapshot)
				if len(snapshot.ObjectMeta.Labels) == 0 || snapshot.ObjectMeta.Labels[utils.LabelKeyContainerSnapshotSourceInstanceName] == "" {
					return []reconcile.Request{}
				}
				return []reconcile.Request{
					{NamespacedName: client.ObjectKey{
						Namespace: snapshot.Namespace,
						Name:      snapshot.ObjectMeta.Labels[utils.LabelKeyContainerSnapshotSourceInstanceName],
					}},
				}
			}))

	if r.Config.IngressVersion == configs.IngressVersionV1 {
		controllerBuilder.Owns(&networkingv1.Ingress{})
	} else if r.Config.IngressVersion == configs.IngressVersionV1Beta1 {
		controllerBuilder.Owns(&networkingv1beta1.Ingress{})
	}

	return controllerBuilder.WithOptions(
		controller.Options{
			MaxConcurrentReconciles: r.Config.MaxConcurrentReconciles,
			RecoverPanic:            true,
		}).Complete(r)
}

//
// helpers
//

func (r *NotebookReconciler) addFinalizer(notebook *dswv1.Notebook) error {
	notebook.AddFinalizer(notebookFinalizer)
	return r.Update(context.Background(), notebook)
}

func (r *NotebookReconciler) handleFinalizer(notebook *dswv1.Notebook) (ctrl.Result, error) {
	if !notebook.HasFinalizer(notebookFinalizer) {
		return ctrl.Result{}, nil
	}

	logger := utils.GetLogger(r.Log, notebook).WithName("handleNotebookFinalizer")
	logger.Info("handleNotebookFinalizer", "notebook", notebook.Name)

	logger.Info("deleting ImageCR", "notebook", notebook.Name)
	if result, err := r.deleteImageCR(notebook); utils.HasErrorOrRequeue(result, err) {
		logger.Error(err, "Failed to delete image CR", "notebook", notebook.Name)
		return result, err
	}

	hasRootfsCloudDiskInNotebook, err := utils.HasRootfsCloudDiskInNotebook(notebook)
	if err != nil {
		logger.Error(err, "Failed to check rootfs cloud disk in notebook")
		return ctrl.Result{}, err
	}
	logger.Info("hasRootfsCloudDiskInNotebook", "notebook", notebook.Name, "hasRootfsCloudDiskInNotebook", hasRootfsCloudDiskInNotebook)

	if hasRootfsCloudDiskInNotebook {
		logger.Info("preparing cloud disk snapshot info", "notebook", notebook.Name)
		if result, err := r.prepareCloudDiskSnapshotInfo(notebook); utils.HasErrorOrRequeue(result, err) {
			logger.Error(err, "Failed to prepare cloud disk snapshot info", "notebook", notebook.Name)
			return result, err
		}
	}

	if paivnode.IsPaiVNodeWorkload(notebook, utils.IsPaiVnodeInstanceEnabled()) {
		if result, err := paivnode.PatchPaiVNodePodDeletionInfo(
			r.Client,
			client.MatchingLabels{utils.LabelInstanceId: notebook.Labels[utils.LabelInstanceId]},
			logger); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}
		if result, err := r.deleteVnodeIngress(notebook); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}
	}

	logger.Info("deleting deployment", "notebook", notebook.Name)
	if result, err := r.deleteDeploy(notebook); utils.HasErrorOrRequeue(result, err) {
		if err != nil {
			logger.Error(err, "Failed to delete deployment", "notebook", notebook.Name)
		}
		return result, err
	}

	if utils.IsNotebookECIEnabled(notebook) {
		if result, err := r.deleteRouteEntryInUserVPC(notebook); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}
	}

	if hasRootfsCloudDiskInNotebook {
		logger.Info("reconcile cloud disk snapshot", "notebook", notebook.Name)
		if result, err := r.DataSourceService.reconcileCloudDiskSnapshot(notebook); utils.HasErrorOrRequeue(result, err) {
			logger.Error(err, "Failed to reconcile cloud disk snapshot", "notebook", notebook.Name)
			return result, err
		}
	}

	_, hasUserCommand := notebook.Annotations[utils.AnnotationUserCommand]
	if r.Config.EnableUserCommand && hasUserCommand {
		if result, err := r.deleteUserCommandConfigMap(notebook); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}
	}

	logger.Info("deleting notebook finalizer", "notebook", notebook.Name)
	notebook.RemoveFinalizer(notebookFinalizer)
	return ctrl.Result{}, r.Update(context.Background(), notebook)
}

func (r *NotebookReconciler) generateClusterIPSvc(notebook *dswv1.Notebook) *corev1.Service {
	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      notebook.Name,
			Namespace: notebook.Namespace,
			Labels:    r.getServiceLabel(notebook),
		},
		Spec: corev1.ServiceSpec{
			Type:      "ClusterIP",
			ClusterIP: "None",
			Selector:  map[string]string{utils.LabelKeyDeployment: notebook.Name},
			Ports:     r.getServicePorts(notebook, utils.IngressClassValueNginx),
		},
	}
}

func (r *NotebookReconciler) generateGlooIngressSvc(notebook *dswv1.Notebook) *corev1.Service {
	serviceLabels := r.getServiceLabel(notebook)
	serviceLabels[utils.LabelKeyEnableGlooDiscovery] = stringUtil.TrueString

	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      notebook.Name + GlooSuffix,
			Namespace: notebook.Namespace,
			Labels:    serviceLabels,
		},
		Spec: corev1.ServiceSpec{
			Type:      "ClusterIP",
			ClusterIP: "None",
			Selector:  map[string]string{utils.LabelKeyDeployment: notebook.Name},
			Ports:     r.getServicePorts(notebook, utils.IngressClassValueGloo),
		},
	}
}

// generateNodePortSvc returns Service object of type:NodePort, because alb ingress only supports NodePort type
// in flannel networks.
func (r *NotebookReconciler) generateNodePortSvc(notebook *dswv1.Notebook) *corev1.Service {
	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      notebook.Name + AlbSuffix,
			Namespace: notebook.Namespace,
			Labels:    r.getServiceLabel(notebook),
			Annotations: map[string]string{
				utils.AnnotationSvcBackendType: utils.DefaultSvcBackendType,
			},
		},
		Spec: corev1.ServiceSpec{
			Type:                  "NodePort",
			ExternalTrafficPolicy: "Local",
			Selector:              map[string]string{utils.LabelKeyDeployment: notebook.Name},
			Ports:                 r.getServicePorts(notebook, utils.IngressClassValueAlb),
		},
	}
}

// generateServices returns Service objects of notebook instance,
// in most cases, it returns one service object only,
// except alb switching gray stage in asi cluster, we will create clusterIp service for nginx ingress and
// NodePort service for alb ingress both, in case of rollback.
func (r *NotebookReconciler) generateServices(notebook *dswv1.Notebook) []*corev1.Service {
	services := make([]*corev1.Service, 0, 1)

	// check ingress-class annotation of notebook
	if value, ok := utils.GetFromMap(notebook.Annotations, utils.AnnotationIngressClass); ok && (r.Config.WithAsiCloud() || r.Config.WithAsiLingjun()) {
		ingressClassValues := strings.Split(value, ",")
		for _, v := range ingressClassValues {
			if v == "nginx" {
				services = append(services, r.generateClusterIPSvc(notebook))
			}
			if strings.Contains(v, "alb") {
				services = append(services, r.generateNodePortSvc(notebook))
			}
			if v == "gloo" {
				services = append(services, r.generateGlooIngressSvc(notebook))
			}
		}
	} else {
		services = append(services, r.generateClusterIPSvc(notebook))
	}

	// add private zone annotations
	privateZoneName, exists := utils.GetPrivateZoneName(notebook)
	if exists {
		pvtzAnnotations := pvtzutil.GeneratePVTZAnnotations(privateZoneName)
		pvtzAnnotations[pvtz.ServiceAnnoKeyName] = notebook.Labels[utils.LabelKeyInstanceUUID] // change key name for convenient use
		r.checkIfPatchInterfaceKey(pvtzAnnotations, notebook)
		for _, svc := range services {
			if svc.Annotations == nil {
				svc.Annotations = pvtzAnnotations
			} else {
				for k, v := range pvtzAnnotations {
					svc.Annotations[k] = v
				}
			}
		}
	}
	return services
}

// nolint:dupl
func (r *NotebookReconciler) reconcileService(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	if len(notebook.Spec.Template.Spec.Containers) == 0 {
		err := fmt.Errorf("notebook spec is invalid")
		logger.Error(err, "notebook spec is invalid, no containers found")
		return ctrl.Result{}, err
	}

	services := r.generateServices(notebook)
	for _, svc := range services {
		if err := controllerutil.SetControllerReference(notebook, svc, r.Scheme); err != nil {
			return ctrl.Result{}, err
		}
		var found = &corev1.Service{}
		var err = r.Get(context.TODO(), types.NamespacedName{Name: svc.Name, Namespace: svc.Namespace}, found)
		if err != nil && apierrs.IsNotFound(err) {
			logger.Info("Creating Service", "namespace", svc.Namespace, "name", svc.Name)
			if err := r.Create(context.TODO(), svc); err != nil && !apierrs.IsAlreadyExists(err) {
				r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "CreateService", fmt.Sprintf("Fail to create service %s/%s", svc.Namespace, svc.Name))
				return ctrl.Result{}, err
			}
			r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "CreateService", fmt.Sprintf("Created service %s/%s", svc.Namespace, svc.Name))
			return ctrl.Result{}, nil
		} else if err != nil {
			return ctrl.Result{}, err
		}
		if err := r.updateExistingServiceIfNecessary(found); err != nil {
			logger.Error(err, "Fail to update service", "namespace", found.Namespace, "name", found.Name)
			return ctrl.Result{}, err
		}
	}

	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) reconcileConfigmap(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	config := map[string]string{}

	// mount to /etc/dsw/config/instance.properties
	configStr, err := r.generateConfigFile(notebook)
	if err != nil {
		return ctrl.Result{}, err
	}
	if configStr != "" {
		config[utils.InstanceConfigFile] = configStr
		configMap := &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:      notebook.Name,
				Namespace: notebook.Namespace,
			},
			Data: config,
		}
		if _, err := r.createConfigmapIfNotExist(configMap, notebook); err != nil {
			return ctrl.Result{}, err
		}
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) reconcileUserCommand(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	// handle user command by creating config map mounted to /etc/dsw/user-command/content.json
	// 1. check if it's needed to handle user command
	annotationValue, ok := notebook.Annotations[utils.AnnotationUserCommand]
	if !r.Config.EnableUserCommand || !ok || stringUtil.IsEmpty(annotationValue) {
		// has no user command to reconcile
		return ctrl.Result{}, nil
	}
	// 2. parse user command & check if it's necessary to fetch user command from oss
	found := &corev1.ConfigMap{}
	userCmdFromAnnotation := UserCommandAnnotation{}
	if err := json.Unmarshal([]byte(annotationValue), &userCmdFromAnnotation); err != nil {
		logger.Error(err, "failed to parse user command")
		return ctrl.Result{}, err
	}
	if err := r.Get(
		context.TODO(), types.NamespacedName{
			Name:      utils.GetUserCommandConfigMapName(notebook.Name),
			Namespace: notebook.Namespace,
		}, found,
	); err == nil {
		// user command config map already existed
		// ignore this part if config map not found
		if found.Data[utils.UserCommandOssKey] == userCmdFromAnnotation.Path {
			logger.Info("user command config map already existed and content is the same")
			return ctrl.Result{}, nil
		}
	}
	// 3. fetch user command
	userCmdFromOss, err := r.fetchAndParseUserCommand(notebook, userCmdFromAnnotation)
	if err != nil {
		logger.Error(err, "failed to fetch user command")
		return ctrl.Result{}, err
	}

	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      utils.GetUserCommandConfigMapName(notebook.Name),
			Namespace: notebook.Namespace,
		},
		Data: map[string]string{
			utils.UserCommandMountFileName: userCmdFromOss,
			utils.UserCommandOssKey:        userCmdFromAnnotation.Path,
		},
	}

	// 4. create config map
	if err := r.Get(
		context.TODO(), types.NamespacedName{
			Name:      utils.GetUserCommandConfigMapName(notebook.Name),
			Namespace: notebook.Namespace,
		}, found,
	); err != nil {
		if apierrs.IsNotFound(err) {
			if _, err := r.createConfigmapIfNotExist(configMap, notebook); err != nil {
				return ctrl.Result{}, err
			}
			return ctrl.Result{}, nil
		} else {
			logger.Error(err, "failed to get config map for user command")
			return ctrl.Result{}, err
		}
	}

	logger.Error(nil, "user command config map already existed, which is abnormal")
	// try to update it
	found.Data = configMap.Data
	if err := r.Update(context.TODO(), found); err != nil {
		r.EventRecorder.Event(
			notebook, corev1.EventTypeWarning, "CreateUserCommand",
			"failed to update configMap",
		)
		return ctrl.Result{}, err
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) fetchAndParseUserCommand(notebook *dswv1.Notebook, userCmdAnnotation UserCommandAnnotation) (string, error) {
	// fetch user command
	logger := utils.GetLogger(r.Log, notebook)
	ossCli, err := ossClient.NewClientWithOptions(context.TODO(), r.Config.AccessKeyId, r.Config.AccessKeySecret,
		r.Config.UserCommandOssEndpoint, ossSdk.Timeout(int64(defaultOssConnectTimeoutInSeconds), int64(defaultOssReadWriteTimeoutInSeconds)))
	if err != nil {
		logger.Error(err, "failed to create oss client when create configMap")
		return "", err
	}
	userCmdContentFromOss, err := ossCli.GetObject(context.TODO(), r.Config.UserCommandOssBucket, userCmdAnnotation.Path)
	if err != nil {
		r.EventRecorder.Event(
			notebook, corev1.EventTypeWarning, "CreateUserCommand",
			"failed to fetch user command from oss",
		)
		logger.Error(err, "failed to fetch user command from oss")
		return "", err
	}
	userCmdCfg := UserCommandContent{}
	if err := json.Unmarshal(userCmdContentFromOss, &userCmdCfg); err != nil {
		logger.Error(err, "failed to unmarshal user command")
		return "", err
	}
	userCmdCfg.OnCreateCmd.Enable = userCmdAnnotation.EnableOnCreate
	userCmdCfg.OnStartCmd.Enable = userCmdAnnotation.EnableOnStart
	userCmdJson, err := json.Marshal(userCmdCfg)
	if err != nil {
		logger.Error(err, "failed to marshal user command")
		return "", err
	}
	return string(userCmdJson), nil
}

func (r *NotebookReconciler) deleteUserCommandConfigMap(notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	found := &corev1.ConfigMap{}
	configMapName := utils.GetUserCommandConfigMapName(notebook.Name)
	namespace := notebook.Namespace
	if err := r.Get(context.TODO(), types.NamespacedName{Name: configMapName, Namespace: namespace}, found); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		} else {
			return ctrl.Result{}, err
		}
	}
	if err := r.Delete(context.TODO(), found); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		}
		logger.Error(err, "Delete user command config map failed", "name", found.Name, "namespace", found.Namespace)
		return ctrl.Result{}, err
	}
	logger.Info("Delete user command config map succeed", "name", found.Name, "namespace", found.Namespace)
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) generateConfigFile(notebook *dswv1.Notebook) (string, error) {
	result := ""
	logger := utils.GetLogger(r.Log, notebook)
	for key, annotation := range notebook.Annotations {
		if utils.IsConfigAnnotationKey(key) {
			if key == utils.AnnotationConfigExtra {
				if annotation != "" {
					extraCfgMap := map[string]string{}
					configValue, err := base64.StdEncoding.DecodeString(annotation)
					if err != nil {
						return "", err
					}
					if err := json.Unmarshal(configValue, &extraCfgMap); err != nil {
						logger.Error(err, "Failed to parse extra config")
						return "", err
					}
					for k, v := range extraCfgMap {
						result += k + "=" + v + "\n"
					}
				}
			} else {
				configKey := strings.Replace(key, utils.AnnotationConfigPrefix, "", 1)
				configValue, err := base64.StdEncoding.DecodeString(annotation)
				if err != nil {
					return "", err
				}
				result += configKey + "=" + string(configValue) + "\n"
			}
		}
	}
	return result, nil
}

func (r *NotebookReconciler) createConfigmapIfNotExist(
	configMap *corev1.ConfigMap, notebook *dswv1.Notebook,
) (*corev1.ConfigMap, error) {

	logger := utils.GetLogger(r.Log, notebook)
	if err := controllerutil.SetControllerReference(notebook, configMap, r.Scheme); err != nil {
		return nil, err
	}
	var found = &corev1.ConfigMap{}
	var err = r.Get(
		context.TODO(), types.NamespacedName{Name: configMap.Name, Namespace: configMap.Namespace}, found,
	)
	if err != nil && apierrs.IsNotFound(err) {
		logger.Info("Creating configMap", "namespace", configMap.Namespace, "name", configMap.Name)
		if err := r.Create(context.TODO(), configMap); err != nil && !apierrs.IsAlreadyExists(err) {
			r.EventRecorder.Event(
				notebook, corev1.EventTypeNormal, "CreateConfigMap",
				fmt.Sprintf("Fail to create configMap %s/%s", configMap.Namespace, configMap.Name),
			)
			return nil, err
		}
		r.EventRecorder.Event(
			notebook, corev1.EventTypeNormal, "CreateConfigMap",
			fmt.Sprintf("Created configMap %s/%s", configMap.Namespace, configMap.Name),
		)
		return configMap, nil
	} else if err != nil {
		return nil, err
	}
	return configMap, nil
}

func (r *NotebookReconciler) getServiceTargetPort(notebook *dswv1.Notebook) int {
	if r.Config.WithSidecar() {
		return sidecarContainerPort
	} else {
		serviceTargetPort := defaultContainerPort
		containerPorts := notebook.Spec.Template.Spec.Containers[0].Ports
		if len(containerPorts) != 0 {
			serviceTargetPort = int(containerPorts[0].ContainerPort)
		}
		return serviceTargetPort
	}
}

func (r *NotebookReconciler) getServicePorts(notebook *dswv1.Notebook, ingressType string) []corev1.ServicePort {
	// Define the desired Service object
	serviceTargetPort := r.getServiceTargetPort(notebook)

	httpPort := corev1.ServicePort{
		Name:       "http-" + notebook.Name,
		Port:       r.getServicePort(ingressType),
		TargetPort: intstr.FromInt(serviceTargetPort),
		Protocol:   "TCP",
	}
	sshPort := corev1.ServicePort{
		Name:       "ssh-" + notebook.Name,
		Port:       defaultServingSSHPort,
		TargetPort: intstr.FromInt(defaultContainerSSHPort),
		Protocol:   "TCP",
	}

	var ports []corev1.ServicePort
	ports = []corev1.ServicePort{
		httpPort,
	}
	if r.Config.WithLight() {
		ports = append(ports, sshPort)
	}
	return ports
}

func (r *NotebookReconciler) getServiceLabel(notebook *dswv1.Notebook) map[string]string {
	serviceLabels := make(map[string]string)
	for k, v := range notebook.Labels {
		serviceLabels[k] = v
	}
	logger := utils.GetLogger(r.Log, notebook)

	if r.Config.WithXingyunMultiCluster() {
		multiClusterLabels := utils.GenerateSvcLabelsForLightMultiCluster()
		err := mergo.Map(&serviceLabels, multiClusterLabels)
		if err != nil {
			logger.Error(err, "Fail to merge multi cluster annotation for svc")
			return nil
		}
	}

	if r.Config.WithAsiInner() {
		serviceLabels["sigma.ali/internal-lb"] = "vipserver"
		serviceLabels["sigma.ali/vipserver-cluster"] = "DEFAULT"
		serviceLabels["sigma.alibaba-inc.com/app-stage"] = "PUBLISH"
		serviceLabels["sigma.alibaba-inc.com/app-unit"] = "CENTER_UNIT.center"
		serviceLabels["sigma.ali/app-name"] = "dsw-resources-inner"
		serviceLabels["workgroup"] = "Default"
	}
	return serviceLabels
}

func mergeEnvVars(envVars []corev1.EnvVar, extraEnvs map[string]string) []corev1.EnvVar {
	envs := make(map[string]string, len(envVars))
	for _, envVar := range envVars {
		envs[envVar.Name] = envVar.Value
	}

	_ = mergo.Map(&envs, extraEnvs)

	res := []corev1.EnvVar{}
	for key, val := range envs {
		res = append(res, corev1.EnvVar{
			Name:  key,
			Value: val,
		})
	}
	return res
}

func (r *NotebookReconciler) getTenantApiServer(instance *dswv1.DswInstance) (string, string, error) {
	logger := utils.GetLogger(r.Log, instance)
	tenantName := utils.GetTenantNamespace(instance)
	tenantAPIServerIP, err := utils.GetApiServerEndpoint(r.Client, tenantName)
	if err != nil {
		logger.Error(err, "Failed to find TenantAPIServerIP")
		return "", "", err
	}
	if len(tenantAPIServerIP) == 0 {
		err = fmt.Errorf("TenantAPIServerIP is not ready yet, namespace: %s, name: %s",
			instance.Namespace, instance.Name)
		logger.Error(err, "Failed to append eci labels and annotations, TenantAPIServerIP is not ready yet")
		return "", "", err
	}
	tenantAPIServerURL := fmt.Sprintf("https://%s:%s", tenantAPIServerIP, utils.DefaultAPIServerPort)
	return tenantAPIServerIP, tenantAPIServerURL, nil
}

func (r *NotebookReconciler) generateECILabelsAndAnnotations(notebook *dswv1.Notebook) (
	map[string]string, map[string]string, error) {

	if !r.Config.WithMultiTenancy() {
		return nil, nil, nil
	}
	logger := utils.GetLogger(r.Log, notebook).WithValues("namespace", utils.DswUserNamespace)

	var instance = &dswv1.DswInstance{}
	err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: utils.DswUserNamespace}, instance)
	if err != nil {
		logger.Error(err, "Failed to find DswInstance")
		return nil, nil, err
	}

	// get tenant api server url
	var tenantAPIServerIP, tenantAPIServerURL string
	if !utils.IsECIIndirectModelEnabled(instance) {
		tenantAPIServerIP, tenantAPIServerURL, err = r.getTenantApiServer(instance)
		if err != nil {
			logger.Error(err, "getTenantApiServer error")
			return nil, nil, err
		}
	}

	// get security group id from resource group status
	var found = &rgv1.ResourceGroup{}
	var securityGroupId string
	rgName := utils.GetResourceGroupName(instance)
	rgNamespace := utils.GetResourceGroupNamespace(r.Config.TenantScopeKey)
	namespacedName := types.NamespacedName{Name: rgName, Namespace: rgNamespace}
	if err = r.Get(context.TODO(), namespacedName, found); err != nil {
		logger.Error(err, "Failed to find ResourceGroup", "resourceGroup", namespacedName)
		return nil, nil, err
	} else {
		// wait until security group ready
		if utils.IsECIIndirectModelEnabled(instance) {
			if found.Status.SecurityGroupStatus != nil {
				securityGroupId = found.Status.SecurityGroupStatus.SecurityGroupID
			}
		} else {
			if found.Status.VResourceGroupStatus != nil && found.Status.VResourceGroupStatus.Status == mtsv1.ResourceStatusReady {
				securityGroupId = found.Status.VResourceGroupStatus.SecurityGroupID
			}
		}
		if len(securityGroupId) == 0 {
			err = fmt.Errorf("VSecurityGroupID is not ready yet, namespace: %s, name: %s", instance.Namespace, instance.Name)
			logger.Error(err, "SecurityGroupID is not ready Failed to append eci labels and annotations")
			return nil, nil, err
		}
	}

	eciLabels := map[string]string{
		utils.LabelECIEnable: stringUtil.TrueString,
	}

	// add fluid sidecar container inject target label
	if utils.ContainsOSSDataSource(instance.Spec.DataSources) || utils.IsDynamicDataSourceMountEnable(instance) {
		eciLabels[utils.LabelFluidSidecarTarget] = utils.DefaultFluidSidecarTarget
	}
	if utils.IsDynamicDataSourceMountEnable(instance) {
		eciLabels[utils.LabelFuseSidecarPostStartInject] = stringUtil.FalseString
	}

	eciAnnotations := map[string]string{
		utils.AnnotationECIUseSpecs:      instance.Spec.EcsType,
		utils.AnnotationECIImageCache:    strconv.FormatBool(instance.Spec.ECISpec.EnableImageCache),
		utils.AnnotationECIExtraStorage:  fmt.Sprintf("%dGi", instance.Spec.SystemDiskSize),
		utils.AnnotationECIAPIServerURL:  tenantAPIServerURL,
		utils.AnnotationECISecurityGroup: securityGroupId,
	}

	if utils.IsECIIndirectModelEnabled(instance) {
		eciAnnotations[utils.AnnotationECIDropApiServerInteractiveCaps] = utils.ECIDropApiServerInteractiveCaps
	}
	if utils.IsDynamicDataSourceMountEnable(instance) {
		eciAnnotations[utils.AnnotationFluidFuseSidecarPropagationMode] = utils.FuseSidecarPropagationModeDirect
	}
	if tenantAPIServerURL != "" {
		eciAnnotations[utils.AnnotationECIAPIServerURL] = tenantAPIServerURL
	}

	if utils.HasRootfsDatasource(instance) {
		eciAnnotations[utils.AnnotationECIPersistentUserdataContainers] = utils.DswNotebookContainerName
	}

	//eci spot not support private poll
	if !utils.IsSpot(instance) {
		eciAnnotations[utils.AnnotationECIPrivatepoolMatchcriteria] = "Open"
	}

	// support pull images from ACR-EE
	if acrInstListStr, ok := utils.GetFromMap(instance.Annotations, utils.ACRAnnotationAcrEEInsts); ok && acrInstListStr != "" {
		eciAnnotations[utils.AnnotationECIDelayPullingImage] = stringUtil.TrueString
	}

	// add dlink annotations to access user vpc
	if instance.Spec.DLink != nil {
		eciAnnotations[utils.AnnotationProductOnECIMode] = utils.DefaultProductOnECIMode
		eciAnnotations[utils.AnnotationECITenantEniSgId] = instance.Spec.DLink.SecurityGroupID
		eciAnnotations[utils.AnnotationECITenantEniVswId] = instance.Spec.DLink.SwitchID

		roleArn := strings.ToLower(instance.Spec.DLink.RoleArn)
		if roleArn == "" {
			err = fmt.Errorf("DLink rolearn is empty, namespace: %s, name: %s", instance.Namespace, instance.Name)
			logger.Error(err, "Failed to append eci labels and annotations")
			return nil, nil, err
		}
		tenantUserId := aliyunUtils.GetUserIdFromRoleArn(instance.Spec.DLink.RoleArn)

		// NOTE(benjin.mbj) use service role chain to let eci have privilege to create eni in user vpc.
		// see https://ata.alibaba-inc.com/articles/181314?spm=ata.********.0.0.6ddc312a90ifjm#EDbs6xsZ
		// for more details.
		tenantRoleArns := []TenantRoleArn{
			{
				RoleType:      "service",
				RoleArn:       utils.GetRoleArn(r.Config.ResourceAccountId, r.Config.DSWToECIRole),
				AssumeRoleFor: r.Config.ResourceAccountId,
			},
			{
				RoleType: "user",
				RoleArn:  utils.GetRoleArn(r.Config.ServiceAccountId, r.Config.ServiceRoleChainRole),
			},
			{
				RoleType:      "service",
				RoleArn:       roleArn,
				AssumeRoleFor: tenantUserId,
			},
		}
		tenantRoleArnsStr, _ := json.Marshal(tenantRoleArns)
		eciAnnotations[utils.AnnotationECITenantArn] = string(tenantRoleArnsStr)

		defaultRouteDev := utils.MainEniDeviceName
		if instance.Spec.DLink.DefaultRoute == utils.SecondaryEniDeviceName {
			defaultRouteDev = utils.SecondaryEniDeviceName
		}
		// Default customRoute when use eth0.
		customRoutes := []CustomRoute{
			// set default route
			{
				Dev:     defaultRouteDev,
				DstCIDR: "0.0.0.0/0",
			},
		}
		if tenantAPIServerIP != "" {
			// add route of tenant api server
			customRoutes = append(customRoutes, CustomRoute{
				Dev:     utils.MainEniDeviceName,
				DstCIDR: fmt.Sprintf("%s/32", tenantAPIServerIP),
			})
		}
		// Set customRoute when use eth1
		if instance.Spec.DLink.DefaultRoute == utils.SecondaryEniDeviceName {
			// Need add more route when use eth1 for default route.
			if r.Config.ECIMainEniCIDR != "" {
				for _, cidr := range strings.Split(r.Config.ECIMainEniCIDR, ",") {
					customRoutes = append(customRoutes, CustomRoute{
						Dev:     utils.MainEniDeviceName,
						DstCIDR: cidr,
					})
				}
			}
			if r.Config.ECISecondaryEniCIDR != "" {
				for _, cidr := range strings.Split(r.Config.ECISecondaryEniCIDR, ",") {
					customRoutes = append(customRoutes, CustomRoute{
						Dev:     utils.SecondaryEniDeviceName,
						DstCIDR: cidr,
					})
				}
			}
		}
		for _, extendedCidr := range instance.Spec.DLink.ExtendedCIDRs {
			customRoutes = append(customRoutes, CustomRoute{
				Dev:     utils.SecondaryEniDeviceName,
				DstCIDR: extendedCidr,
			})
		}
		customRoutesStr, _ := json.Marshal(customRoutes)
		eciAnnotations[utils.AnnotationECICustomRoutes] = string(customRoutesStr)

		if r.Config.EnablePullAcrEEImage {
			customHostsAnnotations, err := r.generateECICustomHostsAnnotations(instance)
			if err != nil {
				msg := "Skip set eci-custom-hosts because an error occurred during generation"
				logger.Error(err, msg)
				r.EventRecorder.Event(instance, corev1.EventTypeWarning,
					"SkippedUpdateAcrHostAliases", fmt.Sprintf("%s: %s", msg, err.Error()))
			} else {
				for k, v := range customHostsAnnotations {
					eciAnnotations[k] = v
				}
			}
		}
	}

	resourceutil.SetDswEciCustomTags(&eciAnnotations, instance.Spec.UserId)

	return eciLabels, eciAnnotations, nil
}

func (r *NotebookReconciler) getPriorityClassName(foundDSWInstance *dswv1.DswInstance) string {
	if r.Config.WithLight() {
		return fmt.Sprintf(
			"%s-%d", paiPriorityClassNamePrefix, foundDSWInstance.Spec.Priority)
	}

	return defaultPriorityClassName
}

func (r *NotebookReconciler) reconcileQueueUnitWhenSchedulingSuspend(notebook *dswv1.Notebook) (ctrl.Result, error) {
	if _, ok := notebook.Annotations[kubeAnnotationKeyOfSchedulingSuspend]; ok {
		logger := utils.GetLogger(r.Log, notebook)
		logger.Info("Notebook is queuing for scheduling suspend", "annotation", kubeAnnotationKeyOfSchedulingSuspend)

		if r.Config.WithLight() || r.Config.EnableAsiQuota {
			if _, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
				Status: dswv1.Queuing,
			}); err != nil {
				return ctrl.Result{}, err
			}

			var foundQueueUnit = &queuev1.QueueUnit{}
			if err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: notebook.Namespace}, foundQueueUnit); err != nil {
				if apierrs.IsNotFound(err) {
					logger.Info("reconcileQueueUnitStatusBySchedulingSuspendAnnotation, annotation scheduling.x-k8s.io/suspend exists, but QueueUnit not found")
					return utils.Requeue(utils.ReconcileInterval)
				}
				return ctrl.Result{}, err
			}
			if utils.GetQueueUnitConsumerRefKind(foundQueueUnit) != dswv1.NotebookKind {
				logger.Info("reconcileQueueUnitStatusBySchedulingSuspendAnnotation, annotation scheduling.x-k8s.io/suspend exists, QueueUnit consumer is not Notebook, ignore.", "kind", utils.GetQueueUnitConsumerRefKind(foundQueueUnit))
				return ctrl.Result{}, nil
			}

			var foundDSWInstance = &dswv1.DswInstance{}
			if err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: utils.DswUserNamespace}, foundDSWInstance); err != nil {
				return ctrl.Result{}, err
			}

			priorityChanged := foundDSWInstance.Spec.Priority != *foundQueueUnit.Spec.Priority
			statusChanged := foundDSWInstance.Status.Status != foundQueueUnit.Annotations[cmv1.AnnotationPAIJobStatus]
			// If priority changed
			if priorityChanged {
				foundQueueUnit.Spec.Priority = &foundDSWInstance.Spec.Priority
				foundQueueUnit.Spec.PriorityClassName = r.getPriorityClassName(foundDSWInstance)

				if r.Config.WithLight() {
					notebook.Spec.Template.Spec.Priority = &foundDSWInstance.Spec.Priority
					notebook.Spec.Template.Spec.PriorityClassName = r.getPriorityClassName(foundDSWInstance)
				}

				r.EventRecorder.Event(foundDSWInstance,
					corev1.EventTypeNormal,
					"reconcileQueueUnit",
					fmt.Sprintf("modifying instance %s queue priority to %d", notebook.Name, foundDSWInstance.Spec.Priority))

				if err := r.Update(context.TODO(), notebook); err != nil {
					return ctrl.Result{}, err
				}
			}
			// If status changed
			if statusChanged {
				foundQueueUnit.Annotations[cmv1.AnnotationPAIJobStatus] = notebook.Status.Status
			}
			// Update queue unit if priority changed or status changed
			if priorityChanged || statusChanged {
				if err := r.Update(context.TODO(), foundQueueUnit); err != nil {
					return ctrl.Result{}, err
				}
			}
		}
		return utils.Requeue(utils.ReconcileInterval)
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) reconcileQueueUnitForAsiCloudOrLingjun(notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	if r.Config.WithAsiCloud() || r.Config.WithAsiLingjun() {
		logger.Info("Notebook is queuing for asi cloud or lingjun")
		// Check queue unit status
		var foundQueueUnit = &queuev1.QueueUnit{}
		if err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: notebook.Namespace}, foundQueueUnit); err != nil {
			if apierrs.IsNotFound(err) {
				return ctrl.Result{}, nil
			}
			return ctrl.Result{}, err
		}
		if utils.GetQueueUnitConsumerRefKind(foundQueueUnit) != dswv1.NotebookKind {
			logger.Info("reconcileQueueUnitForAsiCloudOrLingjun, QueueUnit consumer is not Notebook, ignore", "kind", utils.GetQueueUnitConsumerRefKind(foundQueueUnit))
			return ctrl.Result{}, nil
		}
		if foundQueueUnit.Status.Phase != constants.StatusDequeued {
			logger.Info("Instance is Queueing", "QUStatus", foundQueueUnit.Status)

			if _, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
				Status: dswv1.Queuing,
			}); err != nil {
				return ctrl.Result{}, err
			}
			return utils.Requeue(utils.ReconcileInterval)
		}
		if foundQueueUnit.Annotations == nil || foundQueueUnit.Annotations[cmv1.AnnotationPAIJobStatus] != notebook.Status.Status {
			if foundQueueUnit.Annotations == nil {
				foundQueueUnit.Annotations = make(map[string]string)
			}
			foundQueueUnit.Annotations[cmv1.AnnotationPAIJobStatus] = notebook.Status.Status
			if err := r.Update(context.TODO(), foundQueueUnit); err != nil {
				return ctrl.Result{}, err
			}
		}
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) reconcileQueueUnit(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	if r.Config.WithLight() {
		// Before the kubeDL upgrade is completed in the lightweight environment, the QueueUnit in the lightweight environment is still created by the notebook controller
		_, err := r.createQueueUnitIfNotExist(notebook)
		if err != nil {
			return ctrl.Result{}, err
		}
	}
	// In the future, QueueUnit will be created by DswInstance controller, and Notebook controller will only be
	// responsible for managing instances that have been created by Notebook controller.
	if result, err := r.reconcileQueueUnitWhenSchedulingSuspend(notebook); utils.HasErrorOrRequeue(result, err) {
		return result, err
	}
	return r.reconcileQueueUnitForAsiCloudOrLingjun(notebook)
}

// In the future, QueueUnit will be created by DswInstance controller, and Notebook controller will only be
// responsible for managing instances that have been created by Notebook controller.
func (r *NotebookReconciler) createQueueUnitIfNotExist(notebook *dswv1.Notebook) (ctrl.Result, error) {
	if notebook.Status.Status != dswv1.Starting &&
		notebook.Status.Status != dswv1.ResourceAllocating &&
		notebook.Status.Status != dswv1.EnvPreparing &&
		notebook.Status.Status != dswv1.Queuing {

		return ctrl.Result{}, nil
	}

	logger := utils.GetLogger(r.Log, notebook)
	if r.Config.JobSchedulePolicy != jobSchedulePolicyQueueUnit {
		logger.Info("Ignore creating QueueUnit", "jobSchedulePolicy", r.Config.JobSchedulePolicy)

		return ctrl.Result{}, nil
	}

	// 1. try to figure out if this notebook is scheduled by queue
	// If notebook does not contain quota, return
	if r.Config.WithLight() {
		if _, ok := notebook.Labels[quotaIdKey]; !ok {
			return ctrl.Result{}, nil
		}
	}
	// 2. find the annotation and try to get QueueUnit
	name := notebook.Name
	namespace := notebook.Namespace
	var foundQueueUnit = &queuev1.QueueUnit{}

	err := r.Get(context.TODO(), types.NamespacedName{Name: name, Namespace: namespace}, foundQueueUnit)
	if err != nil && apierrs.IsNotFound(err) {
		// 2.1 there is no specified QueueUnit in k8s
		logger.Info("Creating QueueUnit by notebook controller")

		// 2.2 generate a new QueueUnit
		queueUnit, err := r.generateQueueUnit(notebook)
		if err != nil {
			return ctrl.Result{}, err
		}

		// 2.3 create the QueueUnit
		err = r.Create(context.TODO(), queueUnit)
		if err != nil {
			return ctrl.Result{}, err
		}

		logger.Info("Create QueueUnit by notebook-controller succeed")

		// 2.4 create corresponding Event to inform
		r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "reconcileQueueUnit", fmt.Sprintf("Created QueueUnit %s/%s", namespace, name))
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) generateObjectReference(notebook *dswv1.Notebook) *corev1.ObjectReference {
	return &corev1.ObjectReference{
		APIVersion: dswv1.GroupVersion.Identifier(),
		Kind:       dswv1.NotebookKind,
		Namespace:  notebook.Namespace,
		Name:       notebook.Name,
	}
}

func (r *NotebookReconciler) generateQueueUnit(notebook *dswv1.Notebook) (*queuev1.QueueUnit, error) {
	// 1. build ObjectReference from corresponding Job CR
	objectReference := r.generateObjectReference(notebook)

	// 2. calculate the total resources of this notebook
	resources := notebook.Spec.Template.Spec.Containers[0].Resources.Requests

	labels := map[string]string{}
	annotations := map[string]string{}
	// Set quota label if asi quota is enabled
	if r.Config.EnableAsiQuota {
		labels[asiQuotaNameKey] = notebook.Labels[asiQuotaNameKey]
	}
	// Copy ali/metric- labels from notebook to queue unit
	// Copy quota resource limit config key from notebook to queue unit
	for labelKey, labelValue := range notebook.Labels {
		if strings.HasPrefix(labelKey, utils.LabelKeyPrefix4Metrics) || labelKey == utils.LabelKey4ResourceLimit {
			labels[labelKey] = labelValue
		}
	}

	// Set annotations from notebook to queue unit
	if notebook.Annotations != nil {
		annotations[cmv1.AnnotationPAIJobDisplayName] = notebook.Annotations[cmv1.AnnotationPAIJobDisplayName]
		annotations[cmv1.AnnotationPAIJobCreateTime] = notebook.Annotations[cmv1.AnnotationPAIJobCreateTime]
		annotations[cmv1.AnnotationPAIJobStatus] = notebook.Status.Status
	}

	logger := utils.GetLogger(r.Log, notebook)
	// 3. build QueueUnit
	priority, err := strconv.ParseInt(notebook.Labels[paiPriorityClassNameKey], 10, 32)
	if err != nil {
		logger.Error(err, "Fail to convert priority to int", "priority", priority)
	}
	priority32 := int32(priority)
	qu := &queuev1.QueueUnit{
		ObjectMeta: metav1.ObjectMeta{
			Name:        notebook.Name,
			Namespace:   notebook.Namespace,
			Labels:      labels,
			Annotations: annotations,
		},
		Spec: queuev1.QueueUnitSpec{
			ConsumerRef:       objectReference,
			Priority:          &priority32,
			PriorityClassName: defaultPriorityClassName,
			Resource:          resources,
		},
		Status: queuev1.QueueUnitStatus{
			Phase:   queuev1.Enqueued,
			Message: "the queue unit is enqueued after created",
		},
	}
	if r.Config.EnableAsiQuota {
		qu.Spec.Queue = notebook.Labels[asiQuotaNameKey]
	}

	// Try to set notebook owns queue-unit
	var foundNotebook = &dswv1.Notebook{}
	if err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: notebook.Namespace}, foundNotebook); err != nil {
		return nil, err
	}

	if err := controllerutil.SetControllerReference(foundNotebook, qu, r.Scheme); err != nil {
		return nil, err
	}

	return qu, nil
}

func (r *NotebookReconciler) addCPFSPovOnHostLabelsAndAnnotations(notebook *dswv1.Notebook, podLabels, podAnnotations map[string]string) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	// Add annotations, labels and resource for deployment when bmcpfs datasource of dswinstance is using pov on host infra, ref: https://aliyuque.antfin.com/pai/pds/mfy5nsvdvtlsh6um
	if r.Config.EnableCPFSPovOnHost {
		instance := &dswv1.DswInstance{}
		err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: utils.DswUserNamespace}, instance)
		if err != nil {
			logger.Error(err, "Failed to find DswInstance")
			return ctrl.Result{}, err
		}
		dataSourceSpecs, err := utils.GetDataSourceSpec(instance, r.Client, logger, r.Config)
		if err != nil {
			logger.Error(err, "Failed to get all datasources for", "dswInstanceName", instance.Name)
			return ctrl.Result{}, err
		}
		dataSourceManager := common.NewDataSourceManager(instance.Name, instance.Namespace, "", r.Client, false, dataSourceSpecs)
		annotations, labelMap, usingPov, err := dataSourceManager.GetAnnotationsAndLabels(utils.DswNotebookContainerName, false)
		if err != nil {
			// Stop reconcile if unexpected error occurs when making annotations and labelMap for deployment
			if !dlcUtils.IsPovOnHostInfraError(err) {
				logger.Error(err, "Failed to generate labelMap and annotations from datasources with pov on host infrastructure for dswinstance", "dswInstanceName", instance.Name)
				if _, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
					Status:  dswv1.Failed,
					Message: fmt.Sprintf("Failed to generate annotations for deployment when dsw mounts cpfs with pov on host infra. %s", err.Error()),
				}); err != nil {
					return ctrl.Result{}, err
				}
				r.EventRecorder.Event(notebook, corev1.EventTypeWarning, "Failed", fmt.Sprintf("Fail to generate metadatas when creating deployment %s/%s", notebook.Namespace, notebook.Name))
				// Stop reconcile
				return ctrl.Result{}, err
			} else {
				logger.Error(err, "GetPovOnHostAnnotationsAndLabels with pov on host infra error")
			}
		}
		if usingPov {
			logger.Info("The dswinstance is using pov on host infrastructure for cpfs datasources", "dswinstanceName", instance.Name)
			_ = mergo.Map(&podAnnotations, annotations)
			_ = mergo.Map(&podLabels, labelMap)
			// Note: this label is used by efc sidecar to check auth, ref: https://aliyuque.antfin.com/pai/pds/mfy5nsvdvtlsh6um
			podLabels[utils.LabelKeyMetricTenantId] = instance.Labels[utils.MetricBusinessUserId]
			delete(podAnnotations, cmv1.AnnotationVirtiofsVolumns)
			delete(podAnnotations, cmv1.AnnotationExtraVirtiosfs)
			dlcUtils.SetPovRequestResource(&notebook.Spec.Template.Spec.Containers[0].Resources)
		}
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) generatePodLabelsAndAnnotations(notebook *dswv1.Notebook, podLabels, podAnnotations map[string]string) (ctrl.Result, error) {
	if err := mergo.Map(&podLabels, map[string]string{
		utils.LabelKeyDeployment:  notebook.Name,
		utils.LabelKeyDswInstance: utils.LabelValueTrue,
	}); err != nil {
		return ctrl.Result{}, err
	}
	// copy labels and annotations from notebook cr
	if err := mergo.Map(&podLabels, notebook.Labels); err != nil {
		return ctrl.Result{}, err
	}
	if err := mergo.Map(&podAnnotations, notebook.Annotations); err != nil {
		return ctrl.Result{}, err
	}

	// set pod labels and annotations to enable ECI
	if utils.IsNotebookECIEnabled(notebook) {
		eciLabels, eciAnnotations, err := r.generateECILabelsAndAnnotations(notebook)

		if err != nil {
			return ctrl.Result{}, err
		}
		if err := mergo.Map(&podLabels, eciLabels); err != nil {
			return ctrl.Result{}, err
		}
		if err := mergo.Map(&podAnnotations, eciAnnotations); err != nil {
			return ctrl.Result{}, err
		}
	}

	if result, err := r.addCPFSPovOnHostLabelsAndAnnotations(notebook, podLabels, podAnnotations); utils.HasErrorOrRequeue(result, err) {
		return result, err
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) generateErdmaDependenciesIfNecessary(notebook *dswv1.Notebook, deploy *appsv1.Deployment) error {
	logger := utils.GetLogger(r.Log, notebook)
	var instance = &dswv1.DswInstance{}
	err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: utils.DswUserNamespace}, instance)
	if err != nil {
		logger.Error(err, "Failed to find DswInstance")
		return err
	}

	isERDMAEnabled := false
	if instance.Spec.ChargeType == utils.ChargeTypePrePaid {
		foundQuota := &pqv1.PAIQuota{}
		resourceName := instance.Spec.ResourceGroupID
		err := r.Get(context.TODO(), types.NamespacedName{Name: resourceName}, foundQuota)
		if err != nil {
			logger.Error(err, "Failed to find PAIQuota", "name", resourceName)
			return err
		}

		// erdma only support ecs quota
		if foundQuota.Spec.ResourceType != pqv1.ResourceTypeOfECS {
			return nil
		}

		isERDMAEnabled = utils.IsRDMAEnabled(foundQuota.Spec.Configs)
	} else {
		// TODO support postpaid
	}

	if isERDMAEnabled {
		// set erdma device allocate hints to tell asi device plugin to mount all erdma devices into pod
		err := rdma.SetRdmaDeviceAllocateHintsAnnotation(&deploy.Spec.Template.ObjectMeta)
		if err != nil {
			logger.Error(err, "Failed to set erdma device allocate hints annotation")
			return err
		}
	}

	return nil
}
func (r *NotebookReconciler) generateDeployment(ctx context.Context, notebook *dswv1.Notebook) (*appsv1.Deployment, ctrl.Result, error) {
	replicas := int32(1)
	privilegeEscalation := true
	logger := utils.GetLogger(r.Log, notebook)

	podLabels := map[string]string{}
	podAnnotations := map[string]string{}
	if result, err := r.generatePodLabelsAndAnnotations(notebook, podLabels, podAnnotations); utils.HasErrorOrRequeue(result, err) {
		return nil, result, err
	}

	deploy := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      notebook.Name,
			Namespace: notebook.Namespace,
			Labels:    notebook.Labels,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					utils.LabelKeyDeployment: notebook.Name,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      podLabels,
					Annotations: podAnnotations,
				},
				Spec: notebook.Spec.Template.Spec,
			},
		},
	}
	if utils.IsNotebookLingjunSpot(notebook) {
		finalizers := []string{spotv1.FinalizerLingjunSpot}
		deploy.Spec.Template.Finalizers = append(deploy.Spec.Template.Finalizers, finalizers...)
		deploy.Spec.Template.Annotations[spot.AnnotationLaunchUuid] = string(notebook.UID)
	}

	err := r.generateErdmaDependenciesIfNecessary(notebook, deploy)
	if err != nil {
		logger.Error(err, "Failed to generate erdma dependencies")
		return nil, ctrl.Result{}, err
	}

	if r.Config.WithLight() {
		r.ResetPreemptivePriorityClass(&deploy.Spec.Template)
	}
	// If use eci indirect connection mode, use aliyun's dns service
	if utils.IsNotebookECIIndirectModeEnabled(notebook) {
		dnsConfig := utils.GetDNSConfig()
		privateZoneName, ok := utils.GetPrivateZoneName(notebook)
		if ok {
			dnsConfig.Searches = pvtzutil.GenerateDNSSearchDomains(
				r.Config.PrivateZoneNamePrefix, privateZoneName, notebook.Namespace)
		}
		deploy.Spec.Template.Spec.DNSConfig = dnsConfig
		deploy.Spec.Template.Spec.DNSPolicy = corev1.DNSNone
		// disable AutomountServiceAccountToken & EnableServiceLinks
		automountServiceAccountToken := false
		deploy.Spec.Template.Spec.AutomountServiceAccountToken = &automountServiceAccountToken
		enableServiceLinks := false
		deploy.Spec.Template.Spec.EnableServiceLinks = &enableServiceLinks
	}

	// copy all Notebook labels to the pod including pod default related labels
	l := &deploy.Spec.Template.Labels
	for k, v := range notebook.Labels {
		(*l)[k] = v
	}

	podSpec := &deploy.Spec.Template.Spec
	for idx := range podSpec.Containers {
		container := &podSpec.Containers[idx]
		if container.Name != utils.DswNotebookContainerName {
			continue
		}

		if r.Config.WithAsiCloud() {
			if container.SecurityContext == nil {
				container.SecurityContext = &corev1.SecurityContext{}
			}
			container.SecurityContext.AllowPrivilegeEscalation = &privilegeEscalation
			container.SecurityContext.SeccompProfile = &corev1.SeccompProfile{
				Type: corev1.SeccompProfileTypeRuntimeDefault,
			}
		}

		// Configure main container
		if container.WorkingDir == "" {
			container.WorkingDir = utils.WorkspaceMountPath
		}
		if container.Ports == nil {
			container.Ports = []corev1.ContainerPort{
				{
					ContainerPort: defaultContainerPort,
					Name:          "notebook-port",
					Protocol:      "TCP",
				},
			}
		}
		container.Env = mergeEnvVars(container.Env, map[string]string{
			"JUPYTER_NAME": notebook.Name,
			EnableIngress:  r.getEnableIngress(container.Env),
		})

		// only for test and pre environment
		if utils.IsNotebookECIIndirectModeEnabled(notebook) && r.Config.ECIVMImageId != "" {
			container.Env = mergeEnvVars(container.Env, map[string]string{
				"__ECI_VM_IMAGE_ID__": r.Config.ECIVMImageId,
			})
		}

		// add dsw-env VolumeMount if setup sidecar container enabled.
		if r.Config.WithSetupSidecar() {
			container.VolumeMounts = utils.AppendVolumeMountIfNotExist(container.VolumeMounts, corev1.VolumeMount{
				Name:      "dsw-env",
				MountPath: "/etc/dsw",
				ReadOnly:  false,
			})
		}

		// add docker-sock VolumeMount if dind-sidecar container enabled.
		if r.Config.WithDindSidecar() && featureUtil.GetWhiteListFeatureGate(ctx).Enabled(features.EnableDinDSidecar) {
			container.Env = mergeEnvVars(container.Env, map[string]string{
				"DOCKER_HOST": "unix:///var/docker/proxy/docker.sock",
			})
			container.VolumeMounts = utils.AppendVolumeMountIfNotExist(container.VolumeMounts, corev1.VolumeMount{
				Name:      "docker-sock",
				MountPath: "/var/docker/proxy",
				ReadOnly:  false,
			})
		}

		container.Env = append(container.Env, corev1.EnvVar{
			Name: "DSW_POD_HOSTNAME",
			ValueFrom: &corev1.EnvVarSource{
				FieldRef: &corev1.ObjectFieldSelector{
					FieldPath: "metadata.name",
				},
			},
		})
		configStr, err := r.generateConfigFile(notebook)

		// contains configmap
		if err == nil && configStr != "" {
			container.VolumeMounts = utils.AppendVolumeMountIfNotExist(
				container.VolumeMounts, corev1.VolumeMount{
					Name:      utils.VolumeNameDswConfig,
					MountPath: utils.VolumeMountPathDswConfig,
					ReadOnly:  false,
				},
			)
			podSpec.Volumes = utils.AppendVolumeIfNotExist(
				podSpec.Volumes,
				corev1.Volume{
					Name: utils.VolumeNameDswConfig,
					VolumeSource: corev1.VolumeSource{
						ConfigMap: &corev1.ConfigMapVolumeSource{
							LocalObjectReference: corev1.LocalObjectReference{Name: notebook.Name},
							Items: []corev1.KeyToPath{{
								Key:  utils.InstanceConfigFile,
								Path: utils.InstanceConfigFile}},
						},
					},
				},
			)
		}

		userCmdAnnotationValue, ok := notebook.Annotations[utils.AnnotationUserCommand]
		// contains user command
		if r.Config.EnableUserCommand && ok && stringUtil.IsNotEmpty(userCmdAnnotationValue) {
			container.VolumeMounts = utils.AppendVolumeMountIfNotExist(
				container.VolumeMounts, corev1.VolumeMount{
					Name:      utils.VolumeNameDswUserCommand,
					MountPath: utils.VolumeMountPathDswUserCommand,
					ReadOnly:  false,
				},
			)
			podSpec.Volumes = utils.AppendVolumeIfNotExist(
				podSpec.Volumes,
				corev1.Volume{
					Name: utils.VolumeNameDswUserCommand,
					VolumeSource: corev1.VolumeSource{
						ConfigMap: &corev1.ConfigMapVolumeSource{
							LocalObjectReference: corev1.LocalObjectReference{Name: utils.GetUserCommandConfigMapName(notebook.Name)},
							Items: []corev1.KeyToPath{{
								Key:  utils.UserCommandMountFileName,
								Path: utils.UserCommandMountFileName}},
						},
					},
				},
			)
		}
	}

	if err := r.buildSidecarContainerIfAny(ctx, podSpec, notebook); err != nil {
		logger.Error(err, "Failed to build sidecar container")
		return nil, ctrl.Result{}, err
	}
	// Set owner reference
	if err := controllerutil.SetControllerReference(notebook, deploy, r.Scheme); err != nil {
		return nil, ctrl.Result{}, err
	}

	if err := r.adjustResourceForPccCpfsOverhead(deploy, notebook); err != nil {
		logger.Error(err, "Failed to adjust resource for PCC CPFS overhead")
		return nil, ctrl.Result{}, err
	}

	return deploy, ctrl.Result{}, nil
}

// adjustResourceForPccCpfsOverhead adjusts container cpu and memory limits to account for PCC CPFS overhead
func (r *NotebookReconciler) adjustResourceForPccCpfsOverhead(deploy *appsv1.Deployment, notebook *dswv1.Notebook) error {
	logger := utils.GetLogger(r.Log, notebook)

	hasRootfsCloudDiskInNotebook, err := utils.HasRootfsCloudDiskInNotebook(notebook)
	if err != nil {
		logger.Error(err, "Failed to check rootfs cloud disk in notebook")
		return err
	}
	if !hasRootfsCloudDiskInNotebook {
		return nil
	}

	dataSourceName := utils.GetRootfsDataSourceName(notebook.Name)
	dataSourceNamespace := getRootfsDatasourceNamespace()
	foundDataSource := &dsv1.DataSource{}
	err = r.Get(context.TODO(), types.NamespacedName{Name: dataSourceName, Namespace: dataSourceNamespace}, foundDataSource)
	if err != nil {
		if apierrs.IsNotFound(err) {
			return nil
		}
		logger.Info("Failed to get datasource in k8s", "name", dataSourceName, "datasourceNamespace", dataSourceNamespace)
		return err
	}

	if !utils.IsPccDriver(&foundDataSource.Spec) || foundDataSource.Spec.Type != dsv1.HostPathType {
		return nil
	}

	pccCpfsMemoryOverhead := resource.MustParse(cmv1.PccCpfsMemoryOverhead)
	minMemoryFloor := resource.MustParse(cmv1.RundMinMemory)

	notebookContainer := utils.GetContainer(utils.DswNotebookContainerName, deploy.Spec.Template.Spec.Containers)
	setupContainer := utils.GetContainer(utils.SetupSidecarContainerName, deploy.Spec.Template.Spec.Containers)
	if setupContainer == nil || notebookContainer == nil {
		return nil
	}
	adjustOverheadResourceFlag := false

	for _, resourceMap := range []corev1.ResourceList{notebookContainer.Resources.Requests, notebookContainer.Resources.Limits} {
		if currentMemory, ok := resourceMap[corev1.ResourceMemory]; ok {
			adjustedMemory := currentMemory.DeepCopy()
			adjustedMemory.Sub(pccCpfsMemoryOverhead)
			if adjustedMemory.Cmp(minMemoryFloor) >= 0 {
				// Create a canonical representation to ensure stable comparisons
				// Parse the string representation to normalize the internal format
				canonicalMemory := resource.MustParse(adjustedMemory.String())
				resourceMap[corev1.ResourceMemory] = canonicalMemory
				adjustOverheadResourceFlag = true
			}
		}
	}

	// Add adjust resource to setup container
	if !adjustOverheadResourceFlag {
		return nil
	}
	for _, setupResourceMap := range []corev1.ResourceList{setupContainer.Resources.Requests, setupContainer.Resources.Limits} {
		adjustedSetupMemory := resource.MustParse("0")
		if currentSetupMemory, ok := setupResourceMap[corev1.ResourceMemory]; ok {
			adjustedSetupMemory = currentSetupMemory.DeepCopy()
		}
		adjustedSetupMemory.Add(pccCpfsMemoryOverhead)
		canonicalSetupMemory := resource.MustParse(adjustedSetupMemory.String())
		setupResourceMap[corev1.ResourceMemory] = canonicalSetupMemory
	}

	return nil
}

func (r *NotebookReconciler) reconcileDeployment(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	var found = &appsv1.Deployment{}
	err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: notebook.Namespace}, found)
	if err == nil {
		return ctrl.Result{}, nil
	}
	if apierrs.IsNotFound(err) {
		deploy, result, err := r.generateDeployment(ctx, notebook)
		if utils.HasErrorOrRequeue(result, err) {
			return result, err
		}
		logger.Info("Creating Deployment", "namespace", deploy.Namespace, "name", deploy.Name)
		if err := r.Create(context.TODO(), deploy); err != nil && !apierrs.IsAlreadyExists(err) {
			logger.Error(err, "Failed to create Deployment", "namespace", deploy.Namespace, "name", deploy.Name)

			// If create deployment failed, set the crd status to Failed
			if _, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
				Status:  dswv1.Failed,
				Message: fmt.Sprintf("Fail to create deployment. %s", err.Error()),
			}); err != nil {
				return ctrl.Result{}, err
			}
			r.EventRecorder.Event(notebook, corev1.EventTypeWarning, "Failed", fmt.Sprintf("Fail to create deployment %s/%s", deploy.Namespace, deploy.Name))
			return ctrl.Result{}, err
		} else if err == nil {
			logger.Info("Create Deployment succeed")
			r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "CreateDeployment", fmt.Sprintf("Created deployment %s/%s", deploy.Namespace, deploy.Name))
		}
	} else {
		return ctrl.Result{}, err
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) ResetPreemptivePriorityClass(podTemplate *corev1.PodTemplateSpec) {
	isCanPreemptive, ok := podTemplate.Labels[utils.KoordinatorCanPreemptibleLabel]
	if !ok {
		return
	}
	isPreemptive, ok := podTemplate.Labels[utils.KoordinatorPreemptibleLabel]
	if !ok {
		return
	}
	// oversold job
	if isPreemptive == stringUtil.TrueString && isCanPreemptive == stringUtil.FalseString {
		r.SetPodPriorityClass(podTemplate, utils.OversoldPriority, utils.OversoldPriorityClassName)
	}
	// guaranteed job
	if isPreemptive == stringUtil.FalseString && isCanPreemptive == stringUtil.TrueString {
		r.SetPodPriorityClass(podTemplate, utils.SupportPriority, utils.SupportPriorityClassName)
	}
}

func (r *NotebookReconciler) SetPodPriorityClass(podTemplate *corev1.PodTemplateSpec, priority int32, priorityClass string) {
	podTemplate.Spec.Priority = &priority
	podTemplate.Spec.PriorityClassName = priorityClass
}

func (r *NotebookReconciler) needCreateOrDeleteRouteEntryInUserVPC(instance *dswv1.DswInstance) bool {
	if instance == nil || instance.Spec.DLink == nil {
		return false
	}

	if instance.GetAnnotations() == nil {
		return false
	}

	createRouteEntry, exists := instance.GetAnnotations()[utils.AnnotationCreateRouteEntryInUserVPC]

	return exists && createRouteEntry == stringUtil.TrueString
}

// getTenantEniIdAndNotebookPodIP try to get tenantEniIp and podIP from notebook Pod
func (r *NotebookReconciler) getTenantEniIdAndNotebookPodIP(notebook *dswv1.Notebook) (string, string, error) {
	pod, err := utils.GetNotebookPod(r.Client, notebook.Namespace, notebook.Name)
	if err != nil {
		return "", "", err
	}

	if pod == nil || pod.GetAnnotations() == nil {
		return "", "", nil
	}

	// Try to get tenant eni id from notebook pod
	tenantEniId, exists := pod.GetAnnotations()[utils.AnnotationECITenantEniId]
	if !exists || tenantEniId == "" {
		return "", "", nil
	}

	podIP := pod.Status.PodIP
	if podIP == "" {
		return "", "", nil
	}

	return tenantEniId, podIP, nil
}

func (r *NotebookReconciler) getUserVPCAttributes(instance *dswv1.DswInstance) (string, *vpc.VpcManager, error) {
	dlink := instance.Spec.DLink
	vpcManager, err := vpc.NewVpcManagerWithStsToken(
		dlink.RoleArn,
		r.Config.Region,
		r.Config.EndpointNetwork,
		r.Config.EndpointType,
		r.Config.ServiceAccountAccessKeyId,
		r.Config.ServiceAccountAccessKeySecret,
		r.Config.StsEndpoint)
	if err != nil {
		return "", nil, err
	}

	logger := utils.GetLogger(r.Log, instance)

	// https://help.aliyun.com/document_detail/203494.html
	// Find the first route table as default route table
	routeTableListResp, err := vpcManager.DescribeRouteTableList(dlink.VpcID)
	if err != nil {
		logger.Error(err, "Failed to describe route table list in user vpc")
		return "", nil, err
	}

	var routeTableId string
	// First we try to find the default route table which contains the specified vsw
	for _, routeTable := range routeTableListResp {
		if routeTable.RouteTableType == "System" {
			if slices.Contains(routeTable.VSwitchIds.VSwitchId, dlink.SwitchID) {
				routeTableId = routeTable.RouteTableId
				break
			}
		}
	}

	// Then we try to find custom route table which contains the specified vsw in case default route table
	// is not configured
	if len(routeTableId) == 0 {
		for _, routeTable := range routeTableListResp {
			if slices.Contains(routeTable.VSwitchIds.VSwitchId, dlink.SwitchID) {
				routeTableId = routeTable.RouteTableId
				break
			}
		}
	}

	// Then we try to find custom route table which contains the specified vsw in case default route table
	// is not configured
	if len(routeTableId) == 0 {
		for _, routeTable := range routeTableListResp {
			if slices.Contains(routeTable.VSwitchIds.VSwitchId, dlink.SwitchID) {
				routeTableId = routeTable.RouteTableId
				break
			}
		}
	}

	if len(routeTableId) == 0 {
		logger.Error(fmt.Errorf("routeTableId is empty"), "Failed to describe route table list in user vpc")
		return "", nil, err
	}

	return routeTableId, vpcManager, nil
}

func (r *NotebookReconciler) reconcileRouteEntryInUserVPC(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	if !utils.IsNotebookECIEnabled(notebook) {
		return ctrl.Result{}, nil
	}

	if !r.Config.WithMultiTenancy() {
		return ctrl.Result{}, nil
	}

	logger := utils.GetLogger(r.Log, notebook).WithValues("namespace", utils.DswUserNamespace)
	instance := &dswv1.DswInstance{}
	err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: utils.DswUserNamespace}, instance)
	if err != nil {
		logger.Error(err, "Failed to find DswInstance")
		return ctrl.Result{}, err
	}

	// No need to create route Entry if dlink is nil
	if instance == nil || instance.Spec.DLink == nil {
		return ctrl.Result{}, nil
	}

	if notebook.Annotations[utils.AnnotationRouteEntryInUserVPCStatus] == "created" {
		return ctrl.Result{}, nil
	}

	needCreateRouteEntry := r.needCreateOrDeleteRouteEntryInUserVPC(instance)
	if !needCreateRouteEntry {
		return ctrl.Result{}, nil
	}

	tenantEniId, notebookPodIP, err := r.getTenantEniIdAndNotebookPodIP(notebook)
	if err != nil || tenantEniId == "" || notebookPodIP == "" {
		logger.Info("TenantEniId or notebookPodIP not ready yet")
		return utils.Requeue(utils.ReconcileInterval)
	}

	routeTableId, vpcManager, err := r.getUserVPCAttributes(instance)
	if err != nil {
		logger.Error(err, "Failed to get route table")
		return utils.Requeue(utils.ReconcileBackgroundInterval)
	}

	routeEntryName := fmt.Sprintf("%s_%s", notebook.Name, tenantEniId)
	destCidrBlock := fmt.Sprintf("%s/32", notebookPodIP)

	exists, err := vpcManager.IsRouteEntryExists(routeTableId, routeEntryName)
	if err != nil {
		logger.Error(err, "Failed to check if routeEntry exists", "routeEntryName", routeEntryName, "eni", tenantEniId)
		return utils.Requeue(utils.ReconcileInterval)
	}

	if !exists {
		_, err := vpcManager.CreateRouteEntry(routeEntryName, routeTableId, tenantEniId, destCidrBlock)
		if err != nil {
			logger.Error(err, "Failed to create routeEntry", "routeTableId", routeTableId, "routeEntryName", routeEntryName, "eni", tenantEniId)
			return utils.Requeue(utils.ReconcileInterval)
		}

		if err := r.patchRouteEntryStatus(notebook, "created"); err != nil {
			return ctrl.Result{}, err
		}

		logger.Info("Created routeEntry for eni", "routeTableId", routeTableId, "routeEntryName", routeEntryName, "eni", tenantEniId)
	}

	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) deleteRouteEntryInUserVPC(notebook *dswv1.Notebook) (ctrl.Result, error) {
	if !r.Config.WithMultiTenancy() {
		return ctrl.Result{}, nil
	}

	logger := utils.GetLogger(r.Log, notebook).WithValues("namespace", utils.DswUserNamespace)

	instance := &dswv1.DswInstance{}
	err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: utils.DswUserNamespace}, instance)
	if err != nil {
		logger.Error(err, "Failed to find DswInstance")
		return ctrl.Result{}, err
	}

	// No need to create routeEntry if dlink is nil
	if instance == nil || instance.Spec.DLink == nil {
		return ctrl.Result{}, nil
	}

	needDeleteRouteEntry := r.needCreateOrDeleteRouteEntryInUserVPC(instance)
	if !needDeleteRouteEntry {
		logger.Info("No need to delete routeEntry in user vpc")
		return ctrl.Result{}, nil
	}

	routeTableId, vpcManager, err := r.getUserVPCAttributes(instance)
	if err != nil {
		return ctrl.Result{}, err
	}

	// Remove route entries
	routeEntryListResp, err := vpcManager.DescribeRouteEntryList(routeTableId)
	if err != nil {
		return ctrl.Result{}, err
	}

	routeEntryPrefix := notebook.Name
	for _, re := range routeEntryListResp {
		if !strings.HasPrefix(re.RouteEntryName, routeEntryPrefix) {
			continue
		}

		tenantEniId := utils.GetTenantEniId(re.RouteEntryName)
		if _, err := vpcManager.DeleteRouteEntry(re.RouteEntryId, tenantEniId); err != nil {
			logger.Error(err, "Failed to delete routeEntry", "routeEntryName", re.RouteEntryName, "eni", tenantEniId)
			return utils.Requeue(utils.ReconcileInterval)
		}

		logger.Info("Remove routeEntry for eni", "routeEntryName", re.RouteEntryName, "eni", tenantEniId)
	}

	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) prepareCloudDiskSnapshotInfo(notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook).WithName("prepareCloudDiskSnapshotInfo")

	hasRootfsCloudDiskInNotebook, err := utils.HasRootfsCloudDiskInNotebook(notebook)
	if err != nil {
		logger.Error(err, "Failed to check rootfs cloud disk in notebook")
		return ctrl.Result{}, err
	}

	// Skip no rootfs instances
	if !hasRootfsCloudDiskInNotebook {
		logger.Info("Skip no rootfs notebook", "name", notebook.Name)
		return ctrl.Result{}, nil
	}

	// Snapshot info already patched
	if notebook.Annotations != nil {
		if _, ok := notebook.Annotations[utils.DswAnnotationSnapshotTags]; ok {
			logger.Info("Skip already patched snapshot info notebook", "name", notebook.Name)
			return ctrl.Result{}, nil
		}
	}

	dataSource, err := r.DataSourceService.getRootfsDatasource(notebook.Name)
	if err != nil {
		logger.Error(err, "Failed to get rootfs datasource", "name", notebook.Name)
		return ctrl.Result{}, err
	}
	if dataSource == nil {
		logger.Info("Rootfs datasource does not found", "name", notebook.Name)
		return ctrl.Result{}, nil
	}

	pod, err := utils.GetNotebookPod(r.Client, notebook.Namespace, notebook.Name)
	if err != nil {
		logger.Error(err, "Failed to get notebook pod", "name", notebook.Name)
		return ctrl.Result{}, err
	}

	if pod == nil {
		logger.Info("Notebook pod does not exist", "name", notebook.Name)
		return ctrl.Result{}, nil
	}

	if pod.Status.Phase == corev1.PodPending {
		logger.Info("skip create cloud disk snapshot since pod is pending", "podName", pod.Name)
		return ctrl.Result{}, nil
	}

	var diskID string
	eciID := pod.Annotations[utils.AnnotationECIInstanceId]
	if stringUtil.IsNotEmpty(eciID) {
		diskID, err = r.DataSourceService.getCloudDiskIDFromECI(eciID, dataSource.Name)
		if err != nil {
			return ctrl.Result{}, err
		}
	} else {
		diskID, err = r.DataSourceService.getCloudDiskIDFromPvc(dataSource)
		if err != nil {
			logger.Error(err, "Failed to get cloud disk id from pvc", "name", dataSource.Name)
			return ctrl.Result{}, err
		}
		logger.Info("Get cloud disk id from pvc", "name", dataSource.Name, "diskID", diskID)
	}

	imageID, _, err := r.DataSourceService.getNotebookMainContainerImageInfo(pod)
	if err != nil {
		logger.Error(err, "Failed to get notebook main container image info", "podName", pod.Name)
		return ctrl.Result{}, err
	}

	imageIDSha256 := utils.GetSha256(imageID)
	if stringUtil.IsEmpty(imageIDSha256) {
		logger.Info("imageIDSha256 is empty, use None instead", "podName", pod.Name)
		imageIDSha256 = "None"
	}

	tags := map[string]string{
		dsv1.AttrKeyPvcName:                 dataSource.Name,
		utils.TagKeyDswInstanceName:         notebook.Name,
		utils.TagKeyDswInstancePodName:      pod.Name,
		utils.TagKeyDswDiskID:               diskID,
		utils.TagKeyDswInstanceUUID:         notebook.Labels[utils.LabelKeyDswInstanceUUID],
		utils.TagKeyDswInstanceImageId:      imageIDSha256,
		resourceutil.DSWResourceLevelTagKey: resourceutil.DefaultResourceLevelTagValue,
		resourceutil.DSWTenantNameTagKey:    notebook.Labels[utils.LabelKeyUserId],
	}

	tagsJSON, err := json.Marshal(tags)
	if err != nil {
		logger.Error(err, "Failed to marshal tags", "tags", tags)
		return ctrl.Result{}, err
	}

	if err := r.patchAnnotation(notebook, utils.DswAnnotationSnapshotTags, string(tagsJSON)); err != nil {
		logger.Error(err, "Failed to patch annotation", "notebook", notebook.Name)
		return ctrl.Result{}, err
	}

	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) buildSidecarContainerIfAny(ctx context.Context, podSpec *corev1.PodSpec, notebook *dswv1.Notebook) error {
	imageUrl := r.Config.SidecarImage
	if r.Config.ClusterType == configs.ClusterTypeLightAckPure {
		host, namespace, err := getUserRepoInfoFromEnv()
		if err != nil {
			return err
		}
		imageUrl = fmt.Sprintf("%s/%s/%s", host, namespace, r.Config.SidecarNameTag)
	}
	if r.Config.WithSidecar() {
		notebookContainer := &podSpec.Containers[0]
		for idx := range podSpec.Containers {
			notebookContainer = &podSpec.Containers[idx]
			if notebookContainer.Name == utils.DswNotebookContainerName {
				break
			}
		}
		getEnvFromNotebookContainerEnv := func(envName string) string {
			for _, env := range notebookContainer.Env {
				if env.Name == envName {
					return env.Value
				}
			}
			return ""
		}
		sidecarContainer := corev1.Container{
			Image:           imageUrl,
			ImagePullPolicy: corev1.PullIfNotPresent,
			Name:            utils.DswSidecarContainerName,
			Ports: []corev1.ContainerPort{
				{
					ContainerPort: sidecarContainerPort,
					Protocol:      utils.ProtocolTcp,
				},
			},
			Env: []corev1.EnvVar{
				{
					Name:  utils.ServiceConfigCodeServerAddr,
					Value: getEnvFromNotebookContainerEnv(utils.ServiceConfigCodeServerAddr),
				},
				{
					Name:  utils.ServiceConfigCodeServerPort,
					Value: getEnvFromNotebookContainerEnv(utils.ServiceConfigCodeServerPort),
				},
				{
					Name:  utils.ServiceConfigJupyterServerAddr,
					Value: getEnvFromNotebookContainerEnv(utils.ServiceConfigJupyterServerAddr),
				},
				{
					Name:  utils.ServiceConfigJupyterServerPort,
					Value: getEnvFromNotebookContainerEnv(utils.ServiceConfigJupyterServerPort),
				},
			},
			Resources: corev1.ResourceRequirements{
				Limits:   r.getSidecarResourceLimit(notebook),
				Requests: r.getSidecarResourceRequest(notebook),
			},
		}
		podSpec.Containers = append([]corev1.Container{sidecarContainer}, podSpec.Containers...)
	}

	if r.Config.WithProxySidecar() {
		proxySidecar := corev1.Container{
			Image:           r.Config.ProxySidecarImage,
			ImagePullPolicy: corev1.PullIfNotPresent,
			Name:            utils.ProxySidecarContainerName,
			Ports: []corev1.ContainerPort{
				{
					ContainerPort: proxySidecarPort,
					Protocol:      utils.ProtocolTcp,
				},
			},
			Resources: corev1.ResourceRequirements{
				Limits:   r.getSidecarResourceLimit(notebook),
				Requests: r.getSidecarResourceRequest(notebook),
			},
		}
		podSpec.Containers = append([]corev1.Container{proxySidecar}, podSpec.Containers...)
	}

	if r.Config.WithSetupSidecar() {
		podSpec.Volumes = utils.AppendVolumeIfNotExist(podSpec.Volumes, corev1.Volume{
			Name: "overlay",
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{},
			},
		})
		podSpec.Volumes = utils.AppendVolumeIfNotExist(podSpec.Volumes, corev1.Volume{
			Name: "dsw-env",
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{},
			},
		})

		mountPropagationBidirectional := corev1.MountPropagationBidirectional
		securityContextPrivileged := true

		setupSideCarImage := r.Config.SetupSidecarImage
		for _, c := range podSpec.InitContainers {
			if c.Name != utils.DswInitContainerName {
				continue
			}
			setupSideCarImage = c.Image
		}

		sidecarContainer := corev1.Container{
			Image:           setupSideCarImage,
			ImagePullPolicy: corev1.PullIfNotPresent,
			Name:            utils.SetupSidecarContainerName,
			Resources: corev1.ResourceRequirements{
				Limits:   r.getSidecarResourceLimit(notebook),
				Requests: r.getSidecarResourceRequest(notebook),
			},
			Command: []string{"/bin/bash", "-c", "--"},
			Args: []string{
				"mkdir -p /data/{upper,work} && mount -t overlay -o lowerdir=/etc/dsw,upperdir=/data/upper,workdir=/data/work,index=off overlay /opt/dsw && tail -f /dev/null",
			},
			SecurityContext: &corev1.SecurityContext{
				Privileged: &securityContextPrivileged,
			},
			VolumeMounts: []corev1.VolumeMount{
				{
					Name:      "overlay",
					MountPath: "/data",
					ReadOnly:  false,
				},
				{
					Name:             "dsw-env",
					MountPath:        "/opt/dsw",
					ReadOnly:         false,
					MountPropagation: &mountPropagationBidirectional,
				},
			},
			Lifecycle: &corev1.Lifecycle{
				PreStop: &corev1.LifecycleHandler{
					Exec: &corev1.ExecAction{
						Command: []string{"umount", "-l", "/opt/dsw"},
					},
				},
			},
		}
		podSpec.Containers = append([]corev1.Container{sidecarContainer}, podSpec.Containers...)
	}

	if r.Config.WithDindSidecar() && featureUtil.GetWhiteListFeatureGate(ctx).Enabled(features.EnableDinDSidecar) {
		podSpec.Volumes = utils.AppendVolumeIfNotExist(podSpec.Volumes, corev1.Volume{
			Name: "docker-sock",
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{},
			},
		})
		podSpec.Volumes = utils.AppendVolumeIfNotExist(podSpec.Volumes, corev1.Volume{
			Name: "dind-storage",
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{},
			},
		})
		securityContextPrivileged := true
		dindSideCarImage := r.Config.DindSidecarImage

		dindSidecarContainer := corev1.Container{
			Image:           dindSideCarImage,
			ImagePullPolicy: corev1.PullIfNotPresent,
			Name:            utils.DindSidecarContainerName,
			Resources: corev1.ResourceRequirements{
				Limits:   r.getSidecarResourceLimit(notebook),
				Requests: r.getSidecarResourceRequest(notebook),
			},
			SecurityContext: &corev1.SecurityContext{
				Privileged: &securityContextPrivileged,
			},
			VolumeMounts: []corev1.VolumeMount{
				{
					Name:      "docker-sock",
					MountPath: "/var/run/docker/shared",
					ReadOnly:  false,
				},
				{
					Name:      "dind-storage",
					MountPath: "/var/lib/docker",
					ReadOnly:  false,
				},
			},
		}
		podSpec.Containers = append([]corev1.Container{dindSidecarContainer}, podSpec.Containers...)
	}
	return nil
}

// ECI Pod will use this annotation to set /etc/hosts for pod.
// See more details in: https://help.aliyun.com/document_detail/606947.htm?spm=a2c4g.144561.0.0.24e22542Ju4c6R
func (r *NotebookReconciler) generateECICustomHostsAnnotations(instance *dswv1.DswInstance) (map[string]string, error) {
	if !r.Config.WithMultiTenancy() || !utils.IsECIEnabled(instance) {
		return nil, nil
	}

	customHosts, err := r.getVpcCustomHosts(instance)
	if err != nil || customHosts == nil {
		return nil, err
	}
	customHostsBytes, err := json.Marshal(customHosts)
	if err != nil {
		return nil, err
	}
	return map[string]string{
		utils.AnnotationECICustomHosts: string(customHostsBytes),
	}, nil
}

func (r *NotebookReconciler) getVpcCustomHosts(instance *dswv1.DswInstance) ([]map[string]string, error) {
	logger := utils.GetLogger(r.Log, instance)
	if instance.Spec.DLink == nil || len(instance.Spec.DLink.VpcID) == 0 {
		logger.Info("ignore queries for ACR-EE because VPC-ID is absent")
		return nil, nil
	}

	var customHosts []map[string]string
	if r.Config.AcrVpcEndpoint != stringUtil.EmptyString && r.Config.AcrDomain != stringUtil.EmptyString {
		customHosts = append(customHosts, map[string]string{
			"ip":   r.Config.AcrVpcEndpoint,
			"host": r.Config.AcrDomain,
		})
	}

	roleArn, err := utils.GetDswInstanceRoleArn(instance, r.Config)
	if err != nil {
		return customHosts, err
	}

	crManager, err := cached.NewCachedAcrManager(
		instance.Spec.UserId,
		roleArn,
		r.Config.Region,
		r.Config.EndpointNetwork,
		r.Config.EndpointType,
		r.Config.ServiceAccountAccessKeyId,
		r.Config.ServiceAccountAccessKeySecret,
		r.Config.StsEndpoint)
	if err != nil {
		return customHosts, err
	}

	// the format of vpcHostAlias is:
	//    map[acr-instance-id] hosts-file-content
	// the format of hosts-file-content is like:
	//    ******** user-defined.cr.aliyuncs.com
	//    ******** test-registry-vpc.cr.aliyuncs.com
	var vpcHostAlias map[string]string
	var acrInstanceIDs []string
	if tmpStr, ok := utils.GetFromMap(instance.Annotations, utils.ACRAnnotationAcrEEInsts); ok && tmpStr != "" {
		acrInstanceIDs = strings.Split(tmpStr, ",")
	}
	if len(acrInstanceIDs) > 0 {
		vpcHostAlias, err = crManager.GetVpcHostAlias(acrInstanceIDs, instance.Spec.DLink.VpcID)
	} else {
		vpcHostAlias, err = crManager.GetAllVpcHostAlias(instance.Spec.DLink.VpcID)
	}
	if err != nil || len(vpcHostAlias) == 0 {
		return customHosts, err
	}

	for _, hostsContent := range vpcHostAlias {
		lines := strings.Split(hostsContent, "\n")
		for _, line := range lines {
			if line == "" {
				continue
			}
			hosts := strings.Fields(line)
			if len(hosts) != 2 {
				continue
			}
			customHosts = append(customHosts, map[string]string{
				"ip":   hosts[0],
				"host": hosts[1],
			})
		}
	}
	return customHosts, nil
}

func (r *NotebookReconciler) getEnableIngress(envVars []corev1.EnvVar) string {
	// The environment variable from spec is prioritized.
	// the compatibility for enabling ingress in dsw instance
	for _, envVar := range envVars {
		if envVar.Name == EnableIngress {
			return envVar.Value
		}
	}
	const EnableIngressDefault = stringUtil.TrueString
	const EnableIngressNone = ""
	if r.Config.WithAsiInner() {
		return EnableIngressNone
	}
	return EnableIngressDefault
}

// generateALBIngress returns Ingress object with ingressClassName specified, the field is a reference to an IngressClass
// resource that specifies ALB-Ingress controller
func (r *NotebookReconciler) generateALBIngress(notebook *dswv1.Notebook, ingressClass *string) *networkingv1.Ingress {
	ingressPathType := networkingv1.PathTypePrefix
	ingressRule := networkingv1.IngressRule{
		IngressRuleValue: networkingv1.IngressRuleValue{
			HTTP: &networkingv1.HTTPIngressRuleValue{
				Paths: []networkingv1.HTTPIngressPath{
					{
						PathType: &ingressPathType,
						Path:     "/" + notebook.Name,
						Backend: networkingv1.IngressBackend{
							Service: &networkingv1.IngressServiceBackend{
								Name: notebook.Name + AlbSuffix,
								Port: networkingv1.ServiceBackendPort{
									Number: defaultServingPort,
								},
							},
						},
					},
				},
			},
		},
	}
	// if config.IngressHost is empty string, spec.rules[*].host=""
	ingressRule.Host = r.Config.IngressHost
	return &networkingv1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      notebook.Name + AlbSuffix,
			Namespace: notebook.Namespace,
			Labels:    notebook.Labels,
			Annotations: map[string]string{
				utils.AnnotationSvcBackendType: utils.DefaultSvcBackendType,
			},
		},
		Spec: networkingv1.IngressSpec{
			IngressClassName: ingressClass,
			Rules: []networkingv1.IngressRule{
				ingressRule,
			},
		},
	}
}

func (r *NotebookReconciler) generateGlooIngress(notebook *dswv1.Notebook) *networkingv1.Ingress {
	annotations := map[string]string{
		"kubernetes.io/ingress.class": "gloo",
	}

	ingressPathType := networkingv1.PathTypeImplementationSpecific
	ingressRule := networkingv1.IngressRule{
		IngressRuleValue: networkingv1.IngressRuleValue{
			HTTP: &networkingv1.HTTPIngressRuleValue{
				Paths: []networkingv1.HTTPIngressPath{
					{
						PathType: &ingressPathType,
						Path:     "/" + notebook.Name + "/(.*)",
						Backend: networkingv1.IngressBackend{
							Service: &networkingv1.IngressServiceBackend{
								Name: notebook.Name + GlooSuffix,
								Port: networkingv1.ServiceBackendPort{
									Number: r.getServicePort(utils.IngressClassValueGloo),
								},
							},
						},
					},
				},
			},
		},
	}

	return &networkingv1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:        notebook.Name + GlooSuffix,
			Namespace:   notebook.Namespace,
			Labels:      notebook.Labels,
			Annotations: annotations,
		},
		Spec: networkingv1.IngressSpec{
			Rules: []networkingv1.IngressRule{
				ingressRule,
			},
		},
	}
}

func (r *NotebookReconciler) getServicePort(ingressType string) int32 {
	// Use sidecar port as service port and ingress port
	if utils.IngressClassValueGloo == ingressType {
		return sidecarContainerPort
	}
	return defaultServingPort
}

func (r *NotebookReconciler) generateV1Beta1NginxIngress(notebook *dswv1.Notebook) *networkingv1beta1.Ingress {
	annotations := map[string]string{
		"kubernetes.io/ingress.class":                       r.Config.IngressClass,
		"nginx.ingress.kubernetes.io/rewrite-target":        "/" + notebook.Name + "/$1",
		"nginx.ingress.kubernetes.io/use-regex":             stringUtil.TrueString,
		"nginx.ingress.kubernetes.io/proxy-body-size":       "100M",
		"nginx.ingress.kubernetes.io/proxy-connect-timeout": "1800",
		"nginx.ingress.kubernetes.io/proxy-send-timeout":    "1800",
		"nginx.ingress.kubernetes.io/proxy-read-timeout":    "1800",
	}
	if r.Config.WithLight() {
		// Setup ingress with auth
		annotations["nginx.ingress.kubernetes.io/auth-response-headers"] = "X-Forwarded-User"
		annotations["nginx.ingress.kubernetes.io/auth-url"] =
			fmt.Sprintf("%s?notebookName=%s", r.Config.IngressAuthUrl, notebook.Name)
		annotations["nginx.ingress.kubernetes.io/auth-signin"] = r.Config.IngressAuthSignin
	}
	ingressPathType := networkingv1beta1.PathTypeImplementationSpecific
	ingressRule := networkingv1beta1.IngressRule{
		IngressRuleValue: networkingv1beta1.IngressRuleValue{
			HTTP: &networkingv1beta1.HTTPIngressRuleValue{
				Paths: []networkingv1beta1.HTTPIngressPath{
					{
						PathType: &ingressPathType,
						Path:     "/" + notebook.Name + "/(.*)",
						Backend: networkingv1beta1.IngressBackend{
							ServiceName: notebook.Name,
							ServicePort: intstr.FromInt(defaultServingPort),
						},
					},
				},
			},
		},
	}
	if r.Config.IngressHost != "" {
		ingressRule.Host = r.Config.IngressHost
	}
	return &networkingv1beta1.Ingress{
		ObjectMeta: metav1.ObjectMeta{
			Name:        notebook.Name,
			Namespace:   notebook.Namespace,
			Labels:      notebook.Labels,
			Annotations: annotations,
		},
		Spec: networkingv1beta1.IngressSpec{
			Rules: []networkingv1beta1.IngressRule{
				ingressRule,
			},
		},
	}
}

// generateIngresses returns Ingress object of notebook instance,
// in most cases, it returns one ingress object only,
// except alb switching gray stage in asi cluster, we will create nginx ingress and alb ingress both,
// in case of rollback.
func (r *NotebookReconciler) generateIngresses(notebook *dswv1.Notebook) ([]*networkingv1.Ingress, error) {
	ingresses := make([]*networkingv1.Ingress, 0, 1)

	// vnode cluster need two ingresses:
	// 1. transfer packet from control plane ingress proxy to vnode gateway (with two eni: one for pai vpc, another for user vpc)
	// 2. transfer packet from vnode gateway to notebook
	if paivnode.IsPaiVNodeWorkload(notebook, utils.IsPaiVnodeInstanceEnabled()) {
		instanceIngressGenerator := helper.NewIngressGenerator(helper.IngressClassValueGloo, helper.IngressEndpointVNodeGateway, *r.Config)
		proxyIngressGenerator := helper.NewIngressGenerator(helper.IngressClassValueContour, helper.IngressEndpointInstance, *r.Config)
		instanceIngress, err := instanceIngressGenerator.GenerateIngress(notebook)
		if err != nil {
			r.Log.Error(err, "failed to generate ingress")
			return nil, err
		}
		proxyIngress, err := proxyIngressGenerator.GenerateIngress(notebook)
		if err != nil {
			r.Log.Error(err, "failed to generate ingress")
			return nil, err
		}
		ingresses = append(ingresses, &instanceIngress)
		ingresses = append(ingresses, &proxyIngress)
		return ingresses, nil
	}

	// check ingress-class annotation of notebook
	if value, ok := utils.GetFromMap(notebook.Annotations, utils.AnnotationIngressClass); ok &&
		(r.Config.WithAsiCloud() || r.Config.WithAsiLingjun()) {

		ingressClassValues := strings.Split(value, ",")
		for _, v := range ingressClassValues {
			if utils.IngressClassValueNginx == v {
				ingressGenerator := helper.NewIngressGenerator(utils.IngressClassValueNginx, helper.IngressEndpointInstance, *r.Config)
				ingress, err := ingressGenerator.GenerateIngress(notebook)
				if err != nil {
					r.Log.Error(err, "Failed to generate ingress")
					return nil, err
				}
				ingresses = append(ingresses, &ingress)
			}
			if strings.Contains(v, utils.IngressClassValueAlb) {
				ingresses = append(ingresses, r.generateALBIngress(notebook, &v))
			}
			if utils.IngressClassValueGloo == v {
				ingresses = append(ingresses, r.generateGlooIngress(notebook))
			}
		}
	} else {
		ingressGenerator := helper.NewIngressGenerator(utils.IngressClassValueNginx, helper.IngressEndpointInstance, *r.Config)
		ingress, err := ingressGenerator.GenerateIngress(notebook)
		if err != nil {
			r.Log.Error(err, "Failed to generate ingress")
			return nil, err
		}
		ingresses = append(ingresses, &ingress)
	}
	return ingresses, nil
}

func (r *NotebookReconciler) generateV1Beta1Ingresses(notebook *dswv1.Notebook) []*networkingv1beta1.Ingress {
	ingresses := make([]*networkingv1beta1.Ingress, 0, 1)
	generator := helper.NewV1Beta1NginxIngressGenerator(*r.Config)
	ingress, err := generator.GenerateIngress(notebook)
	if err != nil {
		r.Log.Error(err, "Failed to generate ingress")
		return nil
	}
	ingresses = append(ingresses, &ingress)
	return ingresses
}

// nolint:dupl
func (r *NotebookReconciler) reconcileIngress(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	if r.Config.WithXingyunMultiCluster() {
		return ctrl.Result{}, nil
	}
	logger := utils.GetLogger(r.Log, notebook)
	if r.Config.IngressVersion == configs.IngressVersionV1 {
		ingresses, err := r.generateIngresses(notebook)
		if err != nil {
			return ctrl.Result{}, err
		}
		for _, ing := range ingresses {
			// Set owner reference
			// only setting owner reference in same namespace is permitted
			if notebook.Namespace == ing.Namespace {
				if err := controllerutil.SetControllerReference(notebook, ing, r.Scheme); err != nil {
					return ctrl.Result{}, err
				}
			}
			var found = &networkingv1.Ingress{}
			var err = r.Get(context.TODO(), types.NamespacedName{Name: ing.Name, Namespace: ing.Namespace}, found)
			if err != nil && apierrs.IsNotFound(err) {
				logger.Info("Creating Ingress", "namespace", ing.Namespace, "name", ing.Name)
				if err := r.Create(context.TODO(), ing); err != nil && !apierrs.IsAlreadyExists(err) {
					r.EventRecorder.Event(notebook, corev1.EventTypeWarning, "CreateIngress", fmt.Sprintf("Fail to create ingress %s/%s", ing.Namespace, ing.Name))
					return ctrl.Result{}, err
				}
				r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "CreateIngress", fmt.Sprintf("Created ingress %s/%s", ing.Namespace, ing.Name))
				return ctrl.Result{}, nil
			} else if err != nil {
				return ctrl.Result{}, err
			}
		}
		return ctrl.Result{}, nil
	} else if r.Config.IngressVersion == configs.IngressVersionV1Beta1 {
		ingresses := r.generateV1Beta1Ingresses(notebook)
		for _, ing := range ingresses {
			// Set owner reference
			if err := controllerutil.SetControllerReference(notebook, ing, r.Scheme); err != nil {
				return ctrl.Result{}, err
			}
			var found = &networkingv1beta1.Ingress{}
			var err = r.Get(context.TODO(), types.NamespacedName{Name: ing.Name, Namespace: ing.Namespace}, found)
			if err != nil && apierrs.IsNotFound(err) {
				logger.Info("Creating Ingress", "namespace", ing.Namespace, "name", ing.Name)
				if err := r.Create(context.TODO(), ing); err != nil && !apierrs.IsAlreadyExists(err) {
					r.EventRecorder.Event(notebook, corev1.EventTypeWarning, "CreateIngress", fmt.Sprintf("Fail to create ingress %s/%s", ing.Namespace, ing.Name))
					return ctrl.Result{}, err
				}
				r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "CreateIngress", fmt.Sprintf("Created ingress %s/%s", ing.Namespace, ing.Name))
				return ctrl.Result{}, nil
			} else if err != nil {
				return ctrl.Result{}, err
			}
		}
		return ctrl.Result{}, nil
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) reconcileStatus(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)

	var foundDeploy = &appsv1.Deployment{}
	if err := r.Get(context.TODO(), types.NamespacedName{
		Name:      notebook.Name,
		Namespace: notebook.Namespace,
	}, foundDeploy); err != nil {
		if apierrs.IsNotFound(err) {
			logger.Error(err, "Failed to find deployment")
			return utils.Requeue(utils.ReconcileInterval)
		}
		return ctrl.Result{}, err
	}

	targetStatus, message, podName, err := r.getTargetStatusAndMessage(notebook, foundDeploy)
	if err != nil {
		if dlcUtils.IsThrottledError(err) {
			logger.Error(err, "Failed to get pod targetStatus with throttled error, requeue for a period of time", "targetStatus", targetStatus, "requeueTime", utils.ReconcileBackgroundInterval)
			return utils.Requeue(utils.ReconcileBackgroundInterval)
		} else {
			logger.Error(err, "Failed to get pod targetStatus", "targetStatus", targetStatus)
		}
		return ctrl.Result{}, err
	}

	currentStatus := notebook.Status.Status
	// Handle the state transition from currentStatus to targetStatus
	if currentStatus == dswv1.Running {
		if targetStatus == dswv1.Failed || targetStatus == dswv1.ResourceAllocating || targetStatus == dswv1.EnvPreparing {
			r.logStatusChangeReason(notebook.Name, notebook.Namespace, currentStatus, targetStatus, dswv1.Recovering, "Pod is Recovering")
			targetStatus = dswv1.Recovering
		} else if targetStatus != dswv1.Running {
			r.logStatusChangeReason(notebook.Name, notebook.Namespace, currentStatus, targetStatus, dswv1.Running, "Pod is Running")
			targetStatus = dswv1.Running
		}
	} else if currentStatus == dswv1.Recovering {
		// Status keeps to Recovering when observe suspect Failed targetStatus
		// If instance could not be recovered, user should stop this instance!!!
		if targetStatus != dswv1.Running {
			r.logStatusChangeReason(notebook.Name, notebook.Namespace, currentStatus, targetStatus, dswv1.Recovering, "Pod is still Recovering")
			targetStatus = dswv1.Recovering
		}
	} else if currentStatus == dswv1.Failed {
		// In the case during starting when instance is Failed
		logger.Info("Ignore update status because instance is in final status", "status", notebook.Status)
		return ctrl.Result{}, nil
	} else if currentStatus == dswv1.Saving || currentStatus == dswv1.Saved || currentStatus == dswv1.SaveFailed {
		// if notebook is saving/saved/saveFailed, don't change targetStatus until finished
		return ctrl.Result{}, nil
	} else {
		// Other cases, currentStatus change to targetStatus directly
	}

	// Update message and status and readyReplicas if changed
	if _, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
		Message:       message,
		Status:        targetStatus,
		LastPodName:   podName,
		ReadyReplicas: foundDeploy.Status.ReadyReplicas,
	}); err != nil {
		return ctrl.Result{}, err
	}

	// sync status to resource manager if running
	if targetStatus == dswv1.Running && r.RmcoreClient != nil {
		if err = r.syncResourceOperationToRmgw(notebook); err != nil {
			logger.Error(err, "Failed to sync status to resource manager")
			// not return
		}
	}

	if targetStatus != dswv1.Running {
		logger.Info("Wait until target status to be Running", "targetStatus", targetStatus)
		return utils.Requeue(utils.ReconcileInterval)
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) reconcilePreemptDetection(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	// Detect preemption and add preemption label to notebook
	var foundDSWInstance = &dswv1.DswInstance{}
	if err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: utils.DswUserNamespace}, foundDSWInstance); err != nil {
		return ctrl.Result{}, err
	}

	var pods = &corev1.PodList{}
	if err := r.List(context.TODO(), pods, client.InNamespace(utils.GetNamespace(foundDSWInstance, r.Config)),
		client.MatchingLabels{"deployment": foundDSWInstance.Name}); err != nil {
		return ctrl.Result{}, err
	}

	preemptionLabels := map[string]string{
		persistenceutil.LabelPreemptedByParent:    persistenceutil.PodSubStatusPreemptedByParent,
		persistenceutil.LabelPreemptedBySelfQuota: persistenceutil.PodSubStatusPreemptedBySelfQuota,
		persistenceutil.LabelEvictedByDrainNode:   persistenceutil.PodSubStatusEvictedByDrain,
	}
	preemptedCause := persistenceutil.PodSubStatusPreempted

	for _, p := range pods.Items {
		if p.ObjectMeta.DeletionTimestamp != nil && p.Labels[persistenceutil.LabelPodEviction] == stringUtil.TrueString {
			if preempted, _ := persistenceutil.IsPreemptedPod(&p); preempted {
				for label, status := range preemptionLabels {
					if value, exists := p.Labels[label]; exists && strings.EqualFold(value, stringUtil.TrueString) {
						preemptedCause = status
						break
					}
				}
				if p.Annotations != nil && p.Annotations[utils.AnnotationActualQuotaOversoldType] == utils.QuotaOversoldTypeForce {
					preemptedCause = persistenceutil.OversoldPreemption
				}

				if utils.IsSpotPod(&p) {
					preemptedCause = spot.LingjunSpotPreemption
				}

				if notebook.Labels == nil {
					notebook.Labels = make(map[string]string)
				}

				if _, preemptionLabelExists := notebook.Labels[utils.LabelKeyPreemption]; preemptionLabelExists {
					return ctrl.Result{}, nil
				}
				patch := client.MergeFrom(notebook.DeepCopy())
				notebook.Labels[utils.LabelKeyPreemption] = preemptedCause
				if err := r.Patch(context.TODO(), notebook, patch); err != nil {
					return ctrl.Result{}, err
				}
				return ctrl.Result{}, nil
			}
		}
		if utils.IsEciSpot(foundDSWInstance) && p.Status.Phase == corev1.PodFailed && strings.Contains(p.Status.Reason, spot.BidFailed) {
			logger.Info("Eci instance will be released", "DSWInstance", foundDSWInstance.Name, "Pod", p.Name, "PodStatus", p.Status.Phase)
			failedPod := utils.GetBidFailedPod(pods.Items)

			if result, err := r.prepareEciCloudDiskSnapshotInfo(notebook, failedPod); err != nil {
				logger.Error(err, "Failed to prepare cloud disk snapshot info", "notebook", notebook.Name)
				return result, err
			}

			patch := client.MergeFrom(notebook.DeepCopy())
			notebook.Labels[utils.LabelKeyPreemption] = spot.EciSpotPreemption
			if err := r.Patch(context.TODO(), notebook, patch); err != nil {
				return ctrl.Result{}, err
			}
			return ctrl.Result{}, nil
		}
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) reportNodeError(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	if notebook.Status.Status != dswv1.Running {
		return ctrl.Result{}, nil
	}

	var instance = &dswv1.DswInstance{}
	if err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: utils.DswUserNamespace}, instance); err != nil {
		return ctrl.Result{}, err
	}
	if !r.Config.EnableNodeErrorReport || utils.IsNodeErrorDetected(instance) {
		return ctrl.Result{}, nil
	}

	pod, err := utils.GetNotebookPod(r.Client, utils.GetNamespace(instance, r.Config), instance.Name)
	if err != nil {
		return ctrl.Result{}, err
	}

	nodeErrorReportDetail := pod.Annotations[utils.AnnotationNodeErrorReport]
	if nodeErrorReportDetail == "" {
		return ctrl.Result{}, nil
	}

	// update label and annotation of dswInstance to record node error message
	patch := client.MergeFrom(instance.DeepCopy())
	instance.Labels[utils.LabelKeyNodeError] = stringUtil.TrueString
	instance.Labels[utils.LabelKeyStatusCause] = utils.NodeError
	if instance.Annotations == nil {
		instance.Annotations = make(map[string]string)
	}
	errorDetail := map[string]string{}
	errorDetail[utils.RawNodeErrorReportKey] = nodeErrorReportDetail
	errorDetail[utils.NodeNameKey] = pod.Spec.NodeName
	errorDetailStr, _ := json.Marshal(errorDetail)
	instance.Annotations[utils.AnnotationNodeErrorReport] = string(errorDetailStr)
	return ctrl.Result{}, r.Patch(context.TODO(), instance, patch)
}

func (r *NotebookReconciler) logStatusChangeReason(name, namespace, currentStatus, targetStatusFrom, targetStatusTo, reason string) {
	statusTransitionReason := fmt.Sprintf(
		"CurrentStatus is %s, change targetStatus from %s to %s because %s", currentStatus, targetStatusFrom, targetStatusTo, reason)
	r.Log.Info(statusTransitionReason, "name", name, "namespace", namespace)
}

func (r *NotebookReconciler) reconcileContainerSnapshot(notebook *dswv1.Notebook) (ctrl.Result, error) {
	if notebook.Spec.ImageTemplate.ContainerSnapshotName == "" {
		return ctrl.Result{}, nil
	}
	logger := utils.GetLogger(r.Log, notebook)
	var containerSnapshot = &dswv1.ContainerSnapshot{}
	err := r.Get(context.TODO(), types.NamespacedName{
		Name:      notebook.Spec.ImageTemplate.ContainerSnapshotName,
		Namespace: notebook.Namespace,
	}, containerSnapshot)
	if err != nil {
		if apierrs.IsNotFound(err) {
			err = r.APIReader.Get(context.TODO(), types.NamespacedName{
				Name:      notebook.Spec.ImageTemplate.ContainerSnapshotName,
				Namespace: notebook.Namespace,
			}, containerSnapshot)
			if apierrs.IsNotFound(err) && notebook.Status.Status == dswv1.Saving {
				// when containerSnapshot is deleted, resume notebook status to saving.
				if result, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
					Status: dswv1.Running,
				}); err != nil {
					return result, err
				}
				if err := r.addLatestContainerSnapshotNameLabel(notebook, notebook.Spec.ImageTemplate.ContainerSnapshotName); err != nil {
					return ctrl.Result{}, err
				}
				logger.Info("Resume notebook status to running when containerSnapshot is deleted")
				return utils.Requeue(utils.ReconcileShortInterval)
			}
		}
		return ctrl.Result{}, err
	}

	switch containerSnapshot.Status.Phase {
	case dswv1.ContainerSnapshotPushing, dswv1.ContainerSnapshotSaved, dswv1.ContainerSnapshotFailed, dswv1.ContainerSnapshotInterrupted:
		if notebook.Status.Status == dswv1.Saving {
			if containerSnapshot.Status.Phase == dswv1.ContainerSnapshotPushing && utils.IsContainerSnapshotToPaiRegistry(containerSnapshot) {
				// do not resume instance early for stop-after-saving case
				return utils.Requeue(utils.ReconcileInterval)
			}
			if result, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
				Status: dswv1.Running,
			}); err != nil {
				return result, err
			}
		}
		if err := r.addLatestContainerSnapshotNameLabel(notebook, containerSnapshot.Name); err != nil {
			return ctrl.Result{}, err
		}
	case dswv1.ContainerSnapshotInitialized, dswv1.ContainerSnapshotCommitting:
		if notebook.Status.Status == dswv1.Running {
			if result, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
				Status: dswv1.Saving,
			}); err != nil {
				return result, err
			}
		}
		return utils.Requeue(utils.ReconcileInterval)
	default:
		// No action needed
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) addLatestContainerSnapshotNameLabel(notebook *dswv1.Notebook, containerSnapshotName string) error {
	if len(notebook.Labels) == 0 || notebook.Labels[utils.LabelKeyLatestContainerSnapshotName] != containerSnapshotName {
		// add latest committed snapshot name label to notebook cr
		patch := client.MergeFrom(notebook.DeepCopy())
		if notebook.Labels == nil {
			notebook.Labels = make(map[string]string)
		}
		notebook.Labels[utils.LabelKeyLatestContainerSnapshotName] = containerSnapshotName
		return r.Patch(context.TODO(), notebook, patch)
	}
	return nil
}

func (r *NotebookReconciler) reconcileImage(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	if notebook.Spec.ImageTemplate.ContainerSnapshotName != "" {
		if len(notebook.Labels) > 0 && notebook.Labels[utils.LabelKeyLatestContainerSnapshotName] == notebook.Spec.ImageTemplate.ContainerSnapshotName {
			return ctrl.Result{}, nil
		}
		result, err := r.reconcileContainerSnapshot(notebook)
		if err != nil {
			r.Log.Error(err, "Failed to sync status of containerSnapshot")
		}
		return result, err
	}

	image := &dswv1.Image{
		ObjectMeta: metav1.ObjectMeta{
			Name:      notebook.Name,
			Namespace: notebook.Namespace,
			Labels:    notebook.Labels,
		},
		Spec: dswv1.ImageSpec{
			UserId:             "",
			DockerImageVersion: "",
		},
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(notebook, image, r.Scheme); err != nil {
		return ctrl.Result{}, err
	}

	var foundImage = &dswv1.Image{}
	var err = r.Get(context.TODO(), types.NamespacedName{
		Name:      notebook.Name,
		Namespace: notebook.Namespace,
	}, foundImage)

	logger := utils.GetLogger(r.Log, notebook)
	if err != nil {
		if apierrs.IsNotFound(err) {
			image := &dswv1.Image{
				ObjectMeta: metav1.ObjectMeta{
					Name:      notebook.Name,
					Namespace: notebook.Namespace,
					Labels:    notebook.Labels,
				},
			}

			if r.Config.WithLight() && stringUtil.IsNotBlank(notebook.Annotations[utils.AnnotationDSWImageAuth]) {
				image.Annotations = map[string]string{
					utils.AnnotationDSWImageAuth: notebook.Annotations[utils.AnnotationDSWImageAuth],
				}
			}

			// Set owner reference
			if err := controllerutil.SetControllerReference(notebook, image, r.Scheme); err != nil {
				return ctrl.Result{}, err
			}

			if err := r.Create(context.TODO(), image); err != nil {
				if !apierrs.IsAlreadyExists(err) {
					logger.Error(err, "Failed to create Image CR", "namespace", image.Namespace, "name", image.Name)
					r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "CreateImage", fmt.Sprintf("Fail to create Image %s/%s", image.Namespace, image.Name))
					return ctrl.Result{}, err
				}
				return utils.Requeue(utils.ReconcileInterval)
			} else {
				logger.Info("Create Image CR succeed", "name", image.Name, "namespace", image.Namespace)
				r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "CreateImage", fmt.Sprintf("Created Image %s/%s", image.Namespace, image.Name))
				return utils.Requeue(utils.ReconcileInterval)
			}
		}
		return ctrl.Result{}, err
	}

	if notebook.Status.Status == dswv1.Saving {
		// Handle the case when image spec is patched to empty to cancel saving
		if notebook.Spec.ImageTemplate.DockerImageVersion == "" {
			if result, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
				Status: dswv1.Running,
			}); err != nil {
				return result, err
			}
			return utils.Requeue(utils.ReconcileInterval)
		}

		if r.isEarlyResumeEnabled(foundImage, notebook) {
			// when committing was finished, container would be resumed and ready for use.
			if result, err := r.markAsNotSaving(notebook); err != nil {
				return result, err
			}
			return utils.Requeue(utils.ReconcileShortInterval)
		}

		if !r.isImageFinalState(foundImage, notebook) {
			return utils.Requeue(utils.ReconcileInterval)
		}

		if utils.IsImagePersistenceEnabled(foundImage) {
			if err := r.resetImageCR(foundImage); err != nil {
				logger.Error(err, "Failed to patch null image spec to Image CR",
					"name", foundImage.Name, "namespace", foundImage.Namespace)
				return ctrl.Result{}, err
			}
		}

		lastImageSaveHistory := foundImage.Status.ImageSaveHistory[len(foundImage.Status.ImageSaveHistory)-1]
		saveStatus := lastImageSaveHistory.DockerImageSaveStatus

		if saveStatus == dswv1.ImageStatusSaved || saveStatus == dswv1.ImageStatusFailed {
			targetStatus := dswv1.NotebookStatus{}
			switch saveStatus {
			case dswv1.ImageStatusSaved:
				targetStatus.Status = dswv1.Saved
			case dswv1.ImageStatusFailed:
				targetStatus.Status = dswv1.SaveFailed
				targetStatus.Message = lastImageSaveHistory.Message
			default:
				// Since we've already checked for ImageStatusSaved or ImageStatusFailed,
				// this default case should theoretically never occur.
				err := fmt.Errorf("unknown image save status: %s", saveStatus)
				logger.Error(err, "Unknown image save status", "name", foundImage.Name, "namespace", foundImage.Namespace)
				return ctrl.Result{}, err
			}

			// Attempt to mark notebook status as not saving, regardless of the initial status.
			if result, err := r.markAsNotSaving(notebook); err != nil {
				return result, err
			}

			if result, err := r.updateStatus(notebook, &targetStatus); err != nil {
				return result, err
			}

			return utils.Requeue(utils.ReconcileInterval)
		}
	}

	if notebook.Spec.ImageTemplate.DockerImageVersion != "" {
		if !notebookStatusCanSaveImage.Contains(notebook.Status.Status) {
			logger.Info("Notebook status is not ready to save image", "status", notebook.Status.Status)
			return utils.Requeue(utils.ReconcileInterval)
		}

		if result, err := r.updateImage(notebook, foundImage); err != nil {
			logger.Error(err, "Fail to update image CR template")
			return result, err
		}

		if result, err := r.updateStatus(notebook, &dswv1.NotebookStatus{
			Status: dswv1.Saving,
		}); err != nil {
			return result, err
		}
	}

	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) reconcileAnnotations(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	if notebook.Annotations == nil || notebook.Annotations[utils.DswAnnotationLastRestartTime] == "" {
		return ctrl.Result{}, r.patchLastRestartTime(
			notebook, notebook.CreationTimestamp.Format(timeLayout))
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) isEarlyResumeEnabled(image *dswv1.Image, notebook *dswv1.Notebook) bool {
	// Assuming that user will continue using instance after saving image to his own registry.
	return notebook.Spec.ImageTemplate.AcrRegistryInfo != nil && notebook.Spec.ImageTemplate.AcrRegistryInfo.RegistryType == dswv1.RegistryTypeUser &&
		notebook.Spec.ImageTemplate.ImageId == image.Spec.ImageId && image.Status.Status == dswv1.ImageStatusPushing
}

func (r *NotebookReconciler) isImageFinalState(image *dswv1.Image, notebook *dswv1.Notebook) bool {
	// Get last docker image from image history
	imageSaveHistoryLen := len(image.Status.ImageSaveHistory)
	if imageSaveHistoryLen == 0 {
		return false
	}
	// Get last docker image from image history
	lastImageSaveHistory := image.Status.ImageSaveHistory[imageSaveHistoryLen-1]
	if lastImageSaveHistory.SavedImageId != 0 && lastImageSaveHistory.SavedImageId == notebook.Spec.ImageTemplate.ImageId {
		return true
	}
	return false
}

// update image Cr to start saving image
func (r *NotebookReconciler) updateImage(notebook *dswv1.Notebook, image *dswv1.Image) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	patch := client.MergeFrom(image.DeepCopy())
	image.Spec = notebook.Spec.ImageTemplate
	image.Labels[ImageIdLabelKey] = strconv.FormatInt(notebook.Spec.ImageTemplate.ImageId, 10)
	now := metav1.Now()
	image.Spec.SaveTime = &now
	err := r.Patch(context.TODO(), image, patch)
	if err != nil {
		logger.Error(err, "Fail to patch image template to image CR")
		return ctrl.Result{}, err
	}

	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) resetImageCR(image *dswv1.Image) error {
	patch := client.MergeFrom(image.DeepCopy())
	image.Spec = dswv1.ImageSpec{}
	return r.Patch(context.TODO(), image, patch)
}

func (r *NotebookReconciler) deleteImageCR(notebook *dswv1.Notebook) (ctrl.Result, error) {
	namespacedName := types.NamespacedName{
		Name:      notebook.Name,
		Namespace: notebook.Namespace,
	}
	logger := utils.GetLogger(r.Log, notebook)

	foundImage := &dswv1.Image{}
	if err := r.Get(context.TODO(), namespacedName, foundImage); err != nil {
		if !apierrs.IsNotFound(err) {
			return ctrl.Result{}, err
		}
		return ctrl.Result{}, nil
	}

	if err := r.Delete(context.TODO(), foundImage); err != nil && !apierrs.IsNotFound(err) {
		logger.Error(err, "Fail to delete Image")
		return ctrl.Result{}, err
	}
	return utils.Requeue(utils.ReconcileInterval)
}

func (r *NotebookReconciler) deleteDeploy(notebook *dswv1.Notebook) (ctrl.Result, error) {
	namespacedName := types.NamespacedName{
		Name:      notebook.Name,
		Namespace: notebook.Namespace,
	}
	logger := utils.GetLogger(r.Log, notebook)

	// Make sure that deployment and pod is deleted (Setting the delete option as DeletePropagationForeground)
	found := &appsv1.Deployment{}
	if err := r.Get(context.TODO(), namespacedName, found); err != nil {
		if !apierrs.IsNotFound(err) {
			return utils.Requeue(utils.ReconcileInterval)
		}
		logger.Info("Deployment is deleted")
		return ctrl.Result{}, nil
	}

	prop := metav1.DeletePropagationForeground
	deleteOpts := client.DeleteOptions{
		PropagationPolicy: &prop,
	}
	if err := r.Delete(context.TODO(), found, &deleteOpts); err != nil {
		logger.Error(err, "Fail to delete Deployment")
		return utils.Requeue(utils.ReconcileInterval)
	}
	return utils.Requeue(utils.ReconcileInterval)
}

// deleteVnodeIngress delete the vnode proxy ingress, which is not in the same namespace with the notebook
func (r *NotebookReconciler) deleteVnodeIngress(notebook *dswv1.Notebook) (ctrl.Result, error) {
	clusterId := notebook.Labels[utilsConstant.KeyDlcVClusterId]
	namespacedName := types.NamespacedName{
		Name:      fmt.Sprintf("%s-proxy", notebook.Name),
		Namespace: paivnode.GetPaiVResourceNamespace(clusterId),
	}
	logger := utils.GetLogger(r.Log, notebook)
	// Make sure that deployment and pod is deleted (Setting the delete option as DeletePropagationForeground)
	found := &networkingv1.Ingress{}
	if err := r.Get(context.TODO(), namespacedName, found); err != nil {
		if !apierrs.IsNotFound(err) {
			return utils.Requeue(utils.ReconcileInterval)
		}
		logger.Info("VnodeIngress is deleted")
		return ctrl.Result{}, nil
	}
	prop := metav1.DeletePropagationForeground
	deleteOpts := client.DeleteOptions{
		PropagationPolicy: &prop,
	}
	if err := r.Delete(context.TODO(), found, &deleteOpts); err != nil {
		logger.Error(err, "Fail to delete VnodeIngress")
		return utils.Requeue(utils.ReconcileInterval)
	}
	return utils.Requeue(utils.ReconcileInterval)
}

func (r *NotebookReconciler) getTargetStatusAndMessage(notebook *dswv1.Notebook, deployment *appsv1.Deployment) (string, string, string, error) {
	// set to EnvPreparing first
	var status = dswv1.EnvPreparing
	var message = ""
	var podName = ""

	if utils.IsNotebookECIEnabled(notebook) {
		status = dswv1.ResourceAllocating
	}

	logger := utils.GetLogger(r.Log, notebook)
	pod, err := utils.GetNotebookPod(r.Client, notebook.Namespace, notebook.Name)
	if err != nil {
		return status, message, podName, err
	}

	if pod == nil {
		logger.Info("Notebook pod not found, try to get message from Deployment")
		for _, condition := range deployment.Status.Conditions {
			if condition.Type == appsv1.DeploymentReplicaFailure && condition.Status == corev1.ConditionTrue {
				logger.Info("Get ReplicaFailure message from deployment condition", "condition", condition)
				return dswv1.Failed, condition.Message, podName, nil
			}
		}
		return status, message, podName, nil
	} else {
		podName = pod.Name
	}
	status, message, err = r.getStatusAndMessageByPod(pod, notebook)
	return status, message, podName, err
}

func (r *NotebookReconciler) getStatusAndMessageByReplicaSet(replicaSet *appsv1.ReplicaSet, notebook *dswv1.Notebook) (string, string, error) {
	return "", "", nil
}

func (r *NotebookReconciler) updateStatus(notebook *dswv1.Notebook, status *dswv1.NotebookStatus) (ctrl.Result, error) {
	err := k8sretry.RetryOnConflict(k8sretry.DefaultBackoff, func() error {
		foundNotebook := &dswv1.Notebook{}
		if err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: notebook.Namespace}, foundNotebook); err != nil {
			if apierrs.IsNotFound(err) {
				return nil
			}
			return err
		}

		if notebook.Status.Status != foundNotebook.Status.Status {
			return nil
		}

		statusUpdated := false
		if status.Status != "" {
			if foundNotebook.Status.Status != status.Status {
				r.EventRecorder.Event(notebook, corev1.EventTypeNormal, "UpdateStatus",
					fmt.Sprintf("Status updated from %s to %s", foundNotebook.Status.Status, status.Status))
				foundNotebook.Status.Status = status.Status
				foundNotebook.Status.StatusLastUpdateTime = &metav1.Time{Time: time.Now()}

				if status.Status != dswv1.Failed || status.Message != "" {
					// Should update message at the same time, otherwise the message of previous status will reserve
					// if the status is fail and the message is blank, reserve the status message
					foundNotebook.Status.Message = status.Message
				}
				statusUpdated = true
			}
		}

		if status.Message != "" {
			if foundNotebook.Status.Message != status.Message {
				foundNotebook.Status.Message = status.Message
				statusUpdated = true
			}
		}

		if status.ReadyReplicas != 0 {
			if foundNotebook.Status.ReadyReplicas != status.ReadyReplicas {
				foundNotebook.Status.ReadyReplicas = status.ReadyReplicas
				statusUpdated = true
			}
		}

		if status.LastPodName != "" {
			if foundNotebook.Status.LastPodName != status.LastPodName {
				foundNotebook.Status.LastPodName = status.LastPodName
				statusUpdated = true
			}
		}

		if statusUpdated {
			return r.Status().Update(context.TODO(), foundNotebook)
		}

		return nil
	})

	return ctrl.Result{}, err
}

// remove image template, which means that it shouldn't cause saving
// Image template could be used to check the last save image tag is the same as current target tag.
// If they are the same, the save result is useful to determine whether current save request is success
// So it should be removed if the result has been processed
func (r *NotebookReconciler) markAsNotSaving(notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	err := k8sretry.RetryOnConflict(k8sretry.DefaultBackoff, func() error {
		foundNotebook := &dswv1.Notebook{}
		if err := r.Get(context.TODO(), types.NamespacedName{Name: notebook.Name, Namespace: notebook.Namespace}, foundNotebook); err != nil {
			if apierrs.IsNotFound(err) {
				return nil
			}
			return err
		}

		foundNotebook.Spec.ImageTemplate = dswv1.ImageSpec{}
		err := r.Update(context.TODO(), foundNotebook)
		if err != nil {
			logger.Error(err, "Fail to update image spec of notebook")
			return err
		}

		return nil
	})

	logger.Info("remove ImageTemplate from notebook completely")

	return ctrl.Result{}, err
}

func (r *NotebookReconciler) getPodLastEvent(name string, namespace string) (corev1.Event, error) {
	listOps := &client.ListOptions{
		FieldSelector: fields.OneTermEqualSelector(".involvedObject.name", name),
	}

	var events corev1.EventList
	err := r.List(context.TODO(), &events, client.InNamespace(namespace), listOps)
	if err != nil {
		// It's normal that the event could not be found, so we use r.Log.Info rather than r.Log.Error
		r.Log.Info("Fail to get pod event", "podName", name, "podNamespace", namespace, "err", err)
		return corev1.Event{}, err
	}

	length := len(events.Items)
	if length == 0 {
		return corev1.Event{}, fmt.Errorf("no Event of pod %s exists", name)
	}

	event := events.Items[length-1]
	r.Log.Info("Pod event", "eventMsg", event.Message, "podName", name, "podNamespace", namespace)
	return event, nil
}

func (r *NotebookReconciler) isEciOutOfStock(pod *corev1.Pod) bool {
	if strings.Contains(strings.ToLower(pod.Status.Reason), outOfStockPattern) || strings.Contains(strings.ToLower(pod.Status.Message), outOfStockPattern) {
		return true
	}
	for _, condition := range pod.Status.Conditions {
		if strings.Contains(strings.ToLower(condition.Reason), outOfStockPattern) || strings.Contains(strings.ToLower(condition.Message), outOfStockPattern) {
			return true
		}
	}
	return false
}

func (r *NotebookReconciler) getStatusAndMessageByFailedPod(pod *corev1.Pod, notebook *dswv1.Notebook) (string, string, error) {
	status := string(pod.Status.Phase)
	message := pod.Status.Message
	reason := pod.Status.Reason
	// Should treat UnexpectedAdmissionError as EnvPreparing (can be retried later)
	if reason == "UnexpectedAdmissionError" {
		if utils.IsNotebookECIEnabled(notebook) {
			isContainerInstanceCreated := r.isEciPodContainerInstanceCreated(pod)
			if !isContainerInstanceCreated {
				return dswv1.ResourceAllocating, message, nil
			}
		}
		return dswv1.EnvPreparing, message, nil
	}

	if message == "" {
		// Fall back to pod events if message not found in Pod or Container status
		if event, err := r.getPodLastEvent(pod.GetName(), pod.GetNamespace()); err == nil {
			message = event.Message
		}
	}
	return status, message, nil
}

func (r *NotebookReconciler) isEciPodContainerInstanceCreated(pod *corev1.Pod) bool {
	isContainerInstanceCreated := false

	containerInstanceCreatedCondition := utils.GetPodConditionByType(pod, EciConditionTypeContainerInstanceCreated)
	if containerInstanceCreatedCondition != nil && containerInstanceCreatedCondition.Status == corev1.ConditionTrue {
		isContainerInstanceCreated = true
	}
	return isContainerInstanceCreated
}

func (r *NotebookReconciler) getStatusAndMessageByPendingPod(pod *corev1.Pod, notebook *dswv1.Notebook) (string, string, error) {
	logger := utils.GetLogger(r.Log, notebook).WithValues("podNamespace", pod.Namespace, "podName", pod.Name)
	var message string

	// Handle ECI errors
	if pod.Status.Reason == "ProviderFailed" {
		return dswv1.Failed, pod.Status.Message, nil
	}

	// Handle the case when no stock happens
	if utils.IsNotebookECIEnabled(notebook) {
		isContainerInstanceCreated := r.isEciPodContainerInstanceCreated(pod)
		defaultStatus := dswv1.ResourceAllocating
		if isContainerInstanceCreated {
			defaultStatus = dswv1.EnvPreparing
		}
		logger = logger.WithValues("isContainerInstanceCreated", isContainerInstanceCreated)
		outOfStock := r.isEciOutOfStock(pod)
		if outOfStock {
			eciID := pod.Annotations[utils.AnnotationECIInstanceId]
			if eciID == "" {
				// Note:
				// If the ECI instance id is empty, it means ECI has already tried all the zones
				// and there's out of stock, it is ok to set the status as Failed
				resourceType := notebook.Labels[dswv1.LabelResourceType]
				message = fmt.Sprintf("Your requested resource type [%s] is not enough currently, please try other regions or other resource types", resourceType)
				return dswv1.Failed, message, nil
			} else {
				notebookDiff := time.Now().Sub(notebook.CreationTimestamp.Time)
				if notebookDiff.Seconds() >= defaultPodScheduleTimeOutInSeconds*4 {
					resourceType := notebook.Labels[dswv1.LabelResourceType]
					message = fmt.Sprintf("Your requested resource type [%s] is not enough currently, please try other regions or other resource types", resourceType)
					return dswv1.Failed, message, nil
				}
				// delete eci pod to retry in other zones
				_ = r.Delete(context.Background(), pod)
				logger.Info("Delete pod to retry because ECI is out of stock")
				return dswv1.ResourceAllocating, "", nil
			}
		}

		currentTime := time.Now()

		// Note:
		notebookDiff := currentTime.Sub(notebook.CreationTimestamp.Time)
		if notebookDiff.Seconds() >= defaultPodScheduleTimeOutInSeconds*4 && len(pod.Status.ContainerStatuses) == 0 {
			var latestEvent *corev1.Event = nil
			if event, err := r.getPodLastEvent(pod.GetName(), pod.GetNamespace()); err == nil {
				latestEvent = &event
			}
			if latestEvent != nil {
				logger.Info("Get latest pod events", "event", latestEvent)

				if strings.Contains(strings.ToLower(latestEvent.Message), "out of stock") || strings.Contains(strings.ToLower(latestEvent.Reason), outOfStockPattern) {
					resourceType := notebook.Labels[dswv1.LabelResourceType]
					message = fmt.Sprintf("Your requested resource type [%s] is not enough currently, please try other regions or other resource types", resourceType)
					return dswv1.Failed, message, nil
				}
			}
		}

		// Note:
		// If ECI pod is pending for a while and the Container Status is empty
		// then we can infer that the ECI may be out of stock
		creationTime := pod.CreationTimestamp.Time
		diff := currentTime.Sub(creationTime)
		if diff.Seconds() >= defaultPodScheduleTimeOutInSeconds && len(pod.Status.ContainerStatuses) == 0 {
			if eciID, ok := pod.Annotations[utils.AnnotationECIInstanceId]; ok && eciID != "" {
				// If there is a rootfs cloud disk attached to the ECI then we can wait eci to be ready
				rootfsDataSourceName := fmt.Sprintf("%s-%s", notebook.Name, utils.RootfsCloudDisk)
				diskID, err := r.DataSourceService.getCloudDiskIDFromECI(eciID, rootfsDataSourceName)
				if err != nil {
					logger.Info("Failed to get cloud disk ID")
					return defaultStatus, "", err
				}

				disks, err := r.DataSourceService.CachedEcsManager.DescribeDisks(diskID)
				if err != nil {
					logger.Info("Failed to get cloud disk details", "diskID", diskID)
					return defaultStatus, "", err
				}

				if disks != nil && len(disks.Disks.Disk) > 0 {
					return defaultStatus, "", nil
				}
			}

			// If no eciId or no cloud disk attached to eci
			// then we should delete eci pod to retry in other zones
			_ = r.Delete(context.Background(), pod)
			logger.Info("Delete pod to retry because ECI is timeout")
			return dswv1.ResourceAllocating, "", nil
		}
	}

	// Handle the case when schedule error happens
	for _, cond := range pod.Status.Conditions {
		if cond.Type == corev1.PodScheduled && cond.Reason == corev1.PodReasonUnschedulable {
			currentTime := time.Now()
			creationTime := pod.CreationTimestamp.Time
			diff := currentTime.Sub(creationTime)
			if diff.Seconds() >= defaultPodScheduleTimeOutInSeconds {
				logger.Info("Pod status is failed", "message", pod.Status.Message)
				message = "DSW instance scheduling filed due to insufficient resources, this may be caused by resource fragmentation, node anomalies or other problems."
				if utils.IsSpotPod(pod) {
					message = "The DSW instance spot scheduling failed due to insufficient resources, which may be caused by charging or stock limitations. Please update the instance configuration and try restarting it."
				}
				return dswv1.Failed, message, nil
			}
		}
	}
	return "", "", nil
}

func (r *NotebookReconciler) getStatusAndMessageByPod(pod *corev1.Pod, notebook *dswv1.Notebook) (string, string, error) {
	status := string(pod.Status.Phase)
	var message string
	var err error

	if status == string(corev1.PodFailed) {
		return r.getStatusAndMessageByFailedPod(pod, notebook)
	}
	if status == string(corev1.PodPending) {
		status, message, err = r.getStatusAndMessageByPendingPod(pod, notebook)
		if status != "" {
			return status, message, err
		}
	}

	// Check init container status and message
	status, message, err = r.getStatusMessageByInitContainerStatuses(pod, notebook)
	if err != nil || status == dswv1.Failed {
		// TODO: err is not nil, but return nil
		return status, message, nil // nolint
	}

	// Check container status and message
	status, message, err = r.getStatusMessageByContainerStatuses(pod, notebook)

	// Record pod event if it's failed
	if message == "" && status == dswv1.Failed {
		if event, err := r.getPodLastEvent(pod.GetName(), pod.GetNamespace()); err == nil {
			message = event.Message
		}
	}

	return status, message, err
}

func (r *NotebookReconciler) getStatusMessageByInitContainerStatuses(pod *corev1.Pod, notebook *dswv1.Notebook) (string, string, error) {
	status := dswv1.EnvPreparing
	message := ""

	if utils.IsNotebookECIEnabled(notebook) {
		isContainerInstanceCreated := r.isEciPodContainerInstanceCreated(pod)
		if !isContainerInstanceCreated {
			return dswv1.ResourceAllocating, message, nil
		}
	}

	// Try to get message from init container status when status is Failed
	var cs *corev1.ContainerStatus
	for _, c := range pod.Status.InitContainerStatuses {
		if c.Name == utils.DswInitContainerName {
			cs = &c
			break
		}
	}

	if cs == nil {
		return status, message, nil
	}

	if cs.State.Waiting != nil {
		// Check for image pull failures with timeout
		if dswv1.ContainsString(imagePullFailedReasons, cs.State.Waiting.Reason) {
			if time.Since(pod.CreationTimestamp.Time).Seconds() >= defaultPodScheduleTimeOutInSeconds {
				return dswv1.Failed, cs.State.Waiting.Message, nil
			}
			return status, message, nil
		}

		if dswv1.ContainsString(containerFailReasons, cs.State.Waiting.Reason) {
			return dswv1.Failed, cs.State.Waiting.Message, nil
		}
	}

	return status, message, nil
}

func (r *NotebookReconciler) getStatusMessageByContainerStatuses(pod *corev1.Pod, notebook *dswv1.Notebook) (string, string, error) {
	status := dswv1.EnvPreparing
	message := ""

	if utils.IsNotebookECIEnabled(notebook) {
		isContainerInstanceCreated := r.isEciPodContainerInstanceCreated(pod)
		if !isContainerInstanceCreated {
			return dswv1.ResourceAllocating, message, nil
		}
	}

	// Try to get message from container status when status is Failed
	var cs *corev1.ContainerStatus
	for _, c := range pod.Status.ContainerStatuses {
		if c.Name == utils.DswNotebookContainerName {
			cs = &c
			break
		}
	}

	if cs == nil {
		return status, message, nil
	}

	if cs.State.Waiting != nil {
		// Check for image pull failures with timeout
		if dswv1.ContainsString(imagePullFailedReasons, cs.State.Waiting.Reason) {
			if time.Since(pod.CreationTimestamp.Time).Seconds() >= defaultPodScheduleTimeOutInSeconds {
				return dswv1.Failed, cs.State.Waiting.Message, nil
			}
			return status, message, nil
		}

		if dswv1.ContainsString(containerFailReasons, cs.State.Waiting.Reason) {
			return dswv1.Failed, cs.State.Waiting.Message, nil
		}
	}

	// Container status not running yet
	if cs.State.Running == nil {
		return status, message, nil
	}

	// Container status is not ready yet
	if !cs.Ready {
		if cs.State.Terminated != nil {
			message = fmt.Sprintf("Container was terminated due to %s, exit code: %d", cs.State.Terminated.Reason, cs.State.Terminated.ExitCode)
		}
		return status, message, nil
	}

	// Container never terminated before
	if cs.LastTerminationState.Terminated == nil {
		return dswv1.Running, message, nil
	}

	//
	// Now handle the case when container terminated
	//

	// Container Terminated already been processed just ignore and keep status as Running
	if !r.terminationHappened(cs, notebook) {
		return dswv1.Running, message, nil
	}

	// Patch last restart time
	if err := r.patchLastRestartTime(
		notebook, cs.LastTerminationState.Terminated.FinishedAt.Format(timeLayout)); err != nil {
		return status, message, err
	}

	// This is the case that container Terminated and not yet been processed
	// Check container restart error and report message
	message = cs.LastTerminationState.Terminated.Message
	// If message is not present, try to return the exit code
	if message == "" {
		message = fmt.Sprintf(
			"DSW notebook container restarted at %s. exit code: %d, reason code: %s",
			cs.LastTerminationState.Terminated.FinishedAt.String(),
			cs.LastTerminationState.Terminated.ExitCode,
			cs.LastTerminationState.Terminated.Reason)
	}

	return dswv1.Failed, message, nil
}

func (r *NotebookReconciler) terminationHappened(cs *corev1.ContainerStatus, notebook *dswv1.Notebook) bool {
	if notebook.Annotations == nil {
		return false
	}

	logger := utils.GetLogger(r.Log, notebook)
	lastRestartTime := notebook.Annotations[utils.DswAnnotationLastRestartTime]
	if lastRestartTime == "" {
		return false
	}

	_, err := time.Parse(timeLayout, lastRestartTime)
	if err != nil {
		logger.Info("Failed to parse timestamp", "lastRestartTime", lastRestartTime)
		return false
	}

	terminatedTime := cs.LastTerminationState.Terminated.FinishedAt.Format(timeLayout)
	return terminatedTime > lastRestartTime
}

func (r *NotebookReconciler) patchLastRestartTime(notebook *dswv1.Notebook, lastRestartTime string) error {
	return r.patchAnnotation(notebook, utils.DswAnnotationLastRestartTime, lastRestartTime)
}

func (r *NotebookReconciler) patchRouteEntryStatus(notebook *dswv1.Notebook, status string) error {
	return r.patchAnnotation(notebook, utils.AnnotationRouteEntryInUserVPCStatus, status)
}

func (r *NotebookReconciler) patchAnnotation(notebook *dswv1.Notebook, key, value string) error {
	patch := client.MergeFrom(notebook.DeepCopy())
	if notebook.Annotations == nil {
		notebook.Annotations = make(map[string]string)
	}
	notebook.Annotations[key] = value

	err := r.Patch(context.TODO(), notebook, patch)
	if err != nil {
		logger := utils.GetLogger(r.Log, notebook)
		logger.Error(err, "Fail to patch notebook annotation", "key", key, "value", value)
		return err
	}
	return nil
}

func (r *NotebookReconciler) getSidecarResourceLimit(notebook *dswv1.Notebook) corev1.ResourceList {
	if r.Config.WithAsiLingjun() && !paivnode.IsPaiVNodeWorkload(notebook, utils.IsPaiVnodeInstanceEnabled()) {
		return corev1.ResourceList{
			"koordinator.sh/rdma": resource.MustParse("1"),
		}
	}

	return corev1.ResourceList{
		corev1.ResourceCPU:    resource.MustParse(DswSidecarResourceCPULimit),
		corev1.ResourceMemory: resource.MustParse(DswSidecarResourceMemoryLimit),
	}
}

func (r *NotebookReconciler) getSidecarResourceRequest(notebook *dswv1.Notebook) corev1.ResourceList {
	if r.Config.WithAsiLingjun() && !paivnode.IsPaiVNodeWorkload(notebook, utils.IsPaiVnodeInstanceEnabled()) {
		return corev1.ResourceList{
			"koordinator.sh/rdma": resource.MustParse("1"),
		}
	}

	return corev1.ResourceList{
		corev1.ResourceCPU:    resource.MustParse(DswSidecarResourceCPURequest),
		corev1.ResourceMemory: resource.MustParse(DswSidecarResourceMemoryRequest),
	}
}

// Pod label should be patched only when pod is in running state
func (r *NotebookReconciler) reconcileDynamicPodLabel(ctx context.Context, notebook *dswv1.Notebook) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	labelSelector := labels.SelectorFromSet(map[string]string{
		utils.LabelKeyDeployment: notebook.Name,
	})
	// list pods with this labelSelector
	podList := corev1.PodList{}
	listOpts := &client.ListOptions{
		LabelSelector: labelSelector,
	}
	err := r.List(context.TODO(), &podList, listOpts)
	if err != nil {
		logger.Error(err, "Failed to list pods")
		return ctrl.Result{}, err
	}

	for _, pod := range podList.Items {
		patch := client.MergeFrom(pod.DeepCopy())
		if notebook.Labels == nil {
			notebook.Labels = make(map[string]string)
		}
		if pod.Labels == nil {
			pod.Labels = make(map[string]string)
		}

		notebookLabelValue, notebookLabelExists := notebook.Labels[utils.LabelKeyForwarderEnabled]
		podLabelValue, podLabelExists := pod.Labels[utils.LabelKeyForwarderEnabled]

		needPatch := false
		if !notebookLabelExists && !podLabelExists {
			continue
		} else if !notebookLabelExists && podLabelExists {
			delete(pod.Labels, utils.LabelKeyForwarderEnabled)
			needPatch = true
		} else if notebookLabelExists && !podLabelExists {
			pod.Labels[utils.LabelKeyForwarderEnabled] = notebookLabelValue
			needPatch = true
		} else {
			if notebookLabelValue != podLabelValue {
				pod.Labels[utils.LabelKeyForwarderEnabled] = notebookLabelValue
				needPatch = true
			}
		}

		if needPatch {
			err := r.Patch(context.TODO(), &pod, patch)
			if err != nil {
				logger.Error(err, "Failed to patch pod")
				return ctrl.Result{}, err
			}
			logger.Info("Succeeded patching pod", "podName", pod.Name)
		}
	}
	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) prepareEciCloudDiskSnapshotInfo(notebook *dswv1.Notebook, pod *corev1.Pod) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook).WithName("prepareEciCloudDiskSnapshotInfo")

	hasRootfsCloudDiskInNotebook, err := utils.HasRootfsCloudDiskInNotebook(notebook)
	if err != nil {
		logger.Error(err, "Failed to check rootfs cloud disk in notebook")
		return ctrl.Result{}, err
	}

	// Skip no rootfs instances
	if !hasRootfsCloudDiskInNotebook {
		logger.Info("Skip no rootfs notebook", "name", notebook.Name)
		return ctrl.Result{}, nil
	}

	// Snapshot info already patched
	if notebook.Annotations != nil {
		if _, ok := notebook.Annotations[utils.DswAnnotationSnapshotTags]; ok {
			logger.Info("Skip already patched snapshot info notebook", "name", notebook.Name)
			return ctrl.Result{}, nil
		}
	}

	dataSource, err := r.DataSourceService.getRootfsDatasource(notebook.Name)
	if err != nil {
		logger.Error(err, "Failed to get rootfs datasource", "name", notebook.Name)
		return ctrl.Result{}, err
	}
	if dataSource == nil {
		logger.Info("Rootfs datasource does not found", "name", notebook.Name)
		return ctrl.Result{}, nil
	}
	if pod == nil {
		logger.Info("Notebook pod does not exist", "name", notebook.Name)
		return ctrl.Result{}, nil
	}
	var diskID string
	eciID := pod.Annotations[utils.AnnotationECIInstanceId]
	if resp, err := r.DataSourceService.EcsManager.DescribeDisksByNameAndTags(dataSource.Name,
		&[]ecssdk.DescribeDisksTag{
			{
				Value: eciID,
			},
		},
	); err != nil {
		logger.Error(err, "Invalid cloud disk response", "resp", resp)
		return ctrl.Result{}, err
	} else {
		logger.Info("List notebook pod disk ", "name", notebook.Name, "resp", resp)
		if resp.TotalCount < 1 {
			logger.Info("Notebook pod disk does not exist", "name", notebook.Name)
			return ctrl.Result{}, nil
		} else {
			diskID = resp.Disks.Disk[0].DiskId
		}
	}

	imageID, _, err := r.DataSourceService.getNotebookMainContainerImageInfo(pod)
	if err != nil {
		logger.Error(err, "Failed to get notebook main container image info", "podName", pod.Name)
		return ctrl.Result{}, err
	}

	imageIDSha256 := utils.GetSha256(imageID)
	if stringUtil.IsEmpty(imageIDSha256) {
		logger.Info("imageIDSha256 is empty, use None instead", "podName", pod.Name)
		imageIDSha256 = "None"
	}

	tags := map[string]string{
		dsv1.AttrKeyPvcName:                 dataSource.Name,
		utils.TagKeyDswInstanceName:         notebook.Name,
		utils.TagKeyDswInstancePodName:      pod.Name,
		utils.TagKeyDswDiskID:               diskID,
		utils.TagKeyDswInstanceUUID:         notebook.Labels[utils.LabelKeyDswInstanceUUID],
		utils.TagKeyDswInstanceImageId:      imageIDSha256,
		resourceutil.DSWResourceLevelTagKey: resourceutil.DefaultResourceLevelTagValue,
		resourceutil.DSWTenantNameTagKey:    notebook.Labels[utils.LabelKeyUserId],
	}

	tagsJSON, err := json.Marshal(tags)
	if err != nil {
		logger.Error(err, "Failed to marshal tags", "tags", tags)
		return ctrl.Result{}, err
	}

	if err := r.patchAnnotation(notebook, utils.DswAnnotationSnapshotTags, string(tagsJSON)); err != nil {
		logger.Error(err, "Failed to patch annotation", "notebook", notebook.Name)
		return ctrl.Result{}, err
	}

	return ctrl.Result{}, nil
}

func (r *NotebookReconciler) getPodEvents(name string, namespace string) (corev1.EventList, error) {
	listOps := &client.ListOptions{
		FieldSelector: fields.OneTermEqualSelector(".involvedObject.name", name),
	}

	var events corev1.EventList
	err := r.List(context.TODO(), &events, client.InNamespace(namespace), listOps)
	if err != nil {
		// It's normal that the event could not be found, so we use r.Log.Info rather than r.Log.Error
		r.Log.Info("Fail to get pod event", "podName", name, "podNamespace", namespace, "err", err)
		return corev1.EventList{}, err
	}

	length := len(events.Items)
	if length == 0 {
		return corev1.EventList{}, fmt.Errorf("no Event of pod %s exists", name)
	}
	return events, nil
}

func (r *NotebookReconciler) checkIfPatchInterfaceKey(pvtzAnnotations map[string]string, notebook *dswv1.Notebook) {
	if r.Config.WithAsiCloud() && notebook.Labels[utilsConstant.LabelCniType] == utilsConstant.TerwayCniType {
		pvtzAnnotations[pvtz.ServiceAnnoKeyInterface] = "eth1"
	}
}

func (r *NotebookReconciler) buildSyncResourceOperationRequest(notebook *dswv1.Notebook) *rmcoreSdk.SyncResourceOperationRequest {
	req := &rmcoreSdk.SyncResourceOperationRequest{}
	req.SetAccountId(notebook.Labels[utils.LabelKeyUserId])
	req.SetResourceRegionId(r.RmcoreClient.GetRegion())
	req.SetService(utils.PopServiceCodePaiDsw)
	req.SetResourceType(utils.ResourceInstance)
	req.SetOperationType(notebook.Annotations[utils.AnnotationResourceOperationType])
	req.SetResourceId(notebook.Labels[utils.LabelKeyDswInstanceUUID])
	req.SetOperationTimestamp(time.Now().Unix() * 1000)
	req.SetResourceStatus(dswv1.Running)
	req.SetResourceGuid(notebook.Annotations[utils.AnnotationAliyunResourceGuid])
	commodityCode := notebook.Labels[utils.LabelKeyDswInstanceCommodityCode]
	chargeType := utils.ChargeTypePostPaid
	buyerId := notebook.Labels[utils.LabelKeyUserId]
	commodityInfos := rmcoreSdk.SyncResourceOperationRequestResourceCommodityInfos{
		CommodityCode: &commodityCode,
		ItemCode:      &commodityCode,
		ChargeType:    &chargeType,
		BuyerId:       &buyerId,
	}
	req.SetResourceCommodityInfos([]*rmcoreSdk.SyncResourceOperationRequestResourceCommodityInfos{&commodityInfos})
	return req
}

func (r *NotebookReconciler) syncResourceOperationToRmgw(notebook *dswv1.Notebook) error {
	logger := utils.GetLogger(r.Log, notebook)
	if r.RmcoreClient == nil {
		return nil
	}
	if !isPostpaidInstance(notebook) {
		return nil
	}
	if _, exist := notebook.Annotations[utils.AnnotationAliyunResourceGuid]; !exist {
		return nil
	}
	if _, exist := syncResourceOperationCache.Get(notebook.Name); exist {
		return nil
	}
	syncResourceOperationReq := r.buildSyncResourceOperationRequest(notebook)
	if err := r.RmcoreClient.SyncResourceOperation(context.Background(), syncResourceOperationReq); err != nil {
		return err
	}
	logger.Info("Sync resource operation success")
	syncResourceOperationCache.Add(notebook.Name, nil, time.Hour)
	return nil
}

func isPostpaidInstance(notebook *dswv1.Notebook) bool {
	if val, exist := notebook.Labels[utils.LabelKeyDswBilling]; exist {
		return val == utils.LabelValueTrue
	}
	return false
}

func (r *NotebookReconciler) updateExistingServiceIfNecessary(found *corev1.Service) error {
	needUpdate := false
	// Patch gloo label if not exist
	if found.Labels == nil {
		found.Labels = make(map[string]string)
	}
	if _, ok := found.Labels[utils.LabelKeyEnableGlooDiscovery]; !ok {
		needUpdate = true
		found.Labels[utils.LabelKeyEnableGlooDiscovery] = stringUtil.TrueString
	}

	// delete ssh port for some scenarios
	if !r.Config.WithLight() {
		for i, port := range found.Spec.Ports {
			if port.Port == defaultServingSSHPort {
				found.Spec.Ports = append(found.Spec.Ports[:i], found.Spec.Ports[i+1:]...)
				needUpdate = true
				break
			}
		}
	}
	if needUpdate {
		return r.Update(context.TODO(), found)
	}
	return nil
}
