// TODO: add gosec G601 linter after bump go version to 1.22
// #nosec G601

package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	v1 "pai-mono/apis/pai-dlc-operator/v1"
	"pai-mono/pkg/dsw-controllers/features"
	persistenceutil "pai-mono/pkg/persistence-agent/storage/manager/plugins/pod"
	"pai-mono/pkg/util/grayrelease"
	"pai-mono/pkg/util/paivnode"
	resourceutil "pai-mono/pkg/util/resource"
	"reflect"
	rt "runtime"
	"strconv"
	"strings"
	"time"

	dsv1 "pai-mono/apis/datasource-operator/v1"
	dswv1 "pai-mono/apis/dsw-controllers/v1"
	pqv1 "pai-mono/apis/paiquota/v1"
	rgv1 "pai-mono/apis/resourcegroup/v1"
	"pai-mono/pkg/dsw-controllers/configs"
	"pai-mono/pkg/dsw-controllers/utils"
	featureUtil "pai-mono/pkg/dsw-controllers/utils/features"
	"pai-mono/pkg/pai-dlc-operator/common"
	dlcUtils "pai-mono/pkg/pai-dlc-operator/common/utils"
	"pai-mono/pkg/pai-dlc-operator/pkg/rdma"
	"pai-mono/pkg/pai-resource-service/config/constants"
	resourceutils "pai-mono/pkg/pai-resource-service/utils"
	utilconstants "pai-mono/pkg/util/constants"
	fgUtil "pai-mono/pkg/util/featuregate"
	k8sUtil "pai-mono/pkg/util/k8s"
	logUtil "pai-mono/pkg/util/log"
	"pai-mono/pkg/util/paicredential"
	credentialClient "pai-mono/pkg/util/paicredential/client"
	"pai-mono/pkg/util/spot"
	stringUtil "pai-mono/pkg/util/string"

	spotv1 "gitlab.alibaba-inc.com/pai-dlc/pai-spot-operator/api/v1"

	"github.com/go-logr/logr"
	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	queuev1 "github.com/kube-queue/api/pkg/apis/scheduling/v1alpha1"
	gatewayapi "github.com/solo-io/gloo/projects/gateway/pkg/api/v1"
	gatewayv1 "github.com/solo-io/gloo/projects/gateway/pkg/api/v1/kube/apis/gateway.solo.io/v1"
	gatewayapiv1 "github.com/solo-io/gloo/projects/gloo/pkg/api/v1"
	gatewaycore "github.com/solo-io/solo-kit/pkg/api/v1/resources/core"
	mtsv1 "gitlab.alibaba-inc.com/PAI/mts/apis/external/v1"
	"google.golang.org/protobuf/types/known/wrapperspb"
	corev1 "k8s.io/api/core/v1"
	apierrs "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	k8sretry "k8s.io/client-go/util/retry"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/predicate"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"
)

const (
	dswInstanceFinalizer = "dswinstance.finalizers.dsw.alibaba.com"
)

const (
	hostNameKey              = "kubernetes.io/hostname"
	asiNodeKey               = "sigma.ali/node-sn"
	imageAccelerateModeKey   = "k8s.aliyun.com/image-accelerate-mode"
	imageAccelerateModeValue = "on-demand"
	tenantResourceFinalizer  = "tenantresources.eas.alibaba-inc.com"
	rundFeatureAnnotationKey = "securecontainer.alibabacloud.com/rund-features"
	rundFeatureSgpu          = "feature-sgpu"
	rundFeatureSvaAegis      = "feature-sva-aegis"
	// Ref: https://aliyuque.antfin.com/aone_2052787/ms9o0h/mhn807lql0fg3bu3?singleDoc#
	rundOverrideRuntimeVersionAnnotationKey = "alibabacloud.com/override-runtime-handler"
	kubeAnnotationKeyOfSchedulingSuspend    = "scheduling.x-k8s.io/suspend"
	defaultPriorityClassName                = "high-priority"
	jobSchedulePolicyQueueUnit              = "queue-unit"
	paiPriorityClassNameKey                 = "pai-priority"
	paiPriorityClassNamePrefix              = "pai-priority"
	asiQuotaNameKey                         = "alibabacloud.com/quota-name"
	quotaIdKey                              = "quota-id"
	lrnPatchedKey                           = "lrn-patched"
	rundLatestVersion                       = "rund-latest"
	dswCredentialRefreshIntervalInSeconds   = 3500
)

const (
	defaultShmMemSize         = "1Gi"
	shmVolumeName             = "shm"
	shmVolumeMountPath        = "/dev/shm"
	credentialVolumeName      = "credential-config" // #nosec G101
	credentialVolumeMountPath = "/run/sts/secrets/" // #nosec G101

)

var queueUnitCompareResourceKeys = []string{
	string(corev1.ResourceCPU),
	string(corev1.ResourceMemory),
	string(common.ResourceGPU),
	string(common.ResourcePPU),
	common.AlibabaCloudGpuMemoryRatio,
	common.KoordinatorGpuShared,
	common.KoordinatorGpuCore,
	common.KoordinatorGpuMemoryRatio,
}

// DswInstanceReconciler reconciles a Instance object
type DswInstanceReconciler struct {
	client.Client
	Log                       logr.Logger
	Scheme                    *runtime.Scheme
	APIReader                 client.Reader
	EventRecorder             record.EventRecorder
	Config                    *configs.Config
	DataSourceService         *DataSourceService
	IdleInstanceCullerService *IdleInstanceCullerService
	CredentialManager         credentialClient.Manager
	SpotInstanceManager       *spot.SpotInstanceManager
}

// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=credentials,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=credentials/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=dlc.alibaba.com,resources=resourcegroups,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=dlc.alibaba.com,resources=resourcegroups/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=dlc.alibaba.com,resources=paiquotas,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=dlc.alibaba.com,resources=paiquotas/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=dswinstances,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=dswinstances/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=dswinstances/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=eas.alibaba-inc.k8s.io,resources=tenantresources,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=eas.alibaba-inc.k8s.io,resources=tenantresources/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=core,resources=secrets,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=secrets/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=core,resources=events,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=events/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=core,resources=nodes,verbs=get;list
// +kubebuilder:rbac:groups=core,resources=nodes/status,verbs=get
// +kubebuilder:rbac:groups=dlc.alibaba.com,resources=datasources,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=dlc.alibaba.com,resources=datasources/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=gateway.solo.io,resources=gateways,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=gateway.solo.io,resources=gateways/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=quotas.alibabacloud.com,resources=quotas,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=quotas.alibabacloud.com,resources=quotas/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=base.pai.alibaba-inc.com,resources=credentials,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=base.pai.alibaba-inc.com,resources=credentials/status,verbs=get
// +kubebuilder:rbac:groups=scheduling.eas.alicloud.com,resources=externalclusters,verbs=get;list;watch
// +kubebuilder:rbac:groups=scheduling.eas.alicloud.com,resources=virtualresources,verbs=get;list;watch
// +kubebuilder:rbac:groups=base.pai.alibaba-inc.com,resources=configs,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=spot.alibaba.com,resources=spotinstancetypes,verbs=get;list;watch;create;update;patch;delete;deletecollection
// +kubebuilder:rbac:groups=spot.alibaba.com,resources=spotinstancetypes/status,verbs=get;list;update;patch
// +kubebuilder:rbac:groups=spot.alibaba.com,resources=spotjobbids,verbs=get;list;watch;create;update;patch;delete;deletecollection
// +kubebuilder:rbac:groups=spot.alibaba.com,resources=spotjobbids/status,verbs=get;list;update;patch

// Reconcile is the entrypoint of DswInstanceReconciler
func (r *DswInstanceReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	// Get instance from informer cache by namespaced name
	instance := &dswv1.DswInstance{}
	if err := r.Get(ctx, req.NamespacedName, instance); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}

	// Set logger
	logger := r.Log.WithValues("namespace", instance.Namespace, "name", instance.Name)
	logUtil.SetLoggerToContext(&ctx, logger)
	logger.Info("Reconciling DswInstance")
	defer logger.Info("Finished reconciling DswInstance")

	// Get and register feature gate
	baseFg := featureUtil.GetFeatureGate(ctx)
	fg := fgUtil.NewWhiteListFeatureGate(fgUtil.GetFromAnnotation(instance.Annotations, featureUtil.FeatureGateAnnotationKey), baseFg)
	ctx = featureUtil.WithWhiteListFeatureGate(ctx, fg)

	// Suspend reconciliation if the instance is suspended
	if k8sUtil.IsSuspend(instance) {
		logger.Info("Suspend reconcile, requeue and retry later")
		return ctrl.Result{RequeueAfter: utils.ReconcileBackgroundInterval * time.Second}, nil
	}
	// Check if the spark application is being deleted
	if !instance.DeletionTimestamp.IsZero() {
		return r.handleDswInstanceDeletion(ctx, instance)
	}
	// Handle finalizer
	if !instance.HasFinalizer(dswInstanceFinalizer) {
		return r.handleDswInstanceFinalizer(ctx, instance)
	}
	// Handle preemption
	if result, err := r.handleDswInstancePreemption(instance); utils.HasErrorOrRequeue(result, err) {
		return result, err
	}
	// Reconcile instance timeout
	if withStatusNeedToCheckTimeout(instance.Status.Status) {
		if result, err := r.reconcileInstanceTimeout(instance); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}
	}
	// Complete instance
	if result, err := r.completeDswInstance(ctx, instance); utils.HasErrorOrRequeue(result, err) {
		return result, err
	}
	// Reconcile lightness env
	if r.Config.WithLight() {
		if result, err := r.reconcileDswInstanceInLightness(ctx, instance); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}
	}

	// TODO: Delete this after fsm is stable
	switch instance.Status.Status {
	case "":
		// Update status to Starting by default
		return r.reconcileDswInstanceEmptyStatus(ctx, instance)
	case dswv1.Failed:
		return r.reconcileDswInstanceFailed(ctx, instance)
	}
	// TODO: split logic into fsm
	if result, err := r.reconcileResource(ctx, instance); utils.HasErrorOrRequeue(result, err) {
		logger.Info("reconcile ResourceGroup need requeue", "err", err, "requeue", result.Requeue)
		return result, err
	}
	// TODO: split logic into fsm
	for _, reconcileFunc := range []func(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error){
		r.reconcileCredential,
		r.reconcileImagePullSecrets,
		r.reconcileDataSources,
		r.reconcileInstanceInfoConfigMap,
		r.reconcileKoordinatorGpuResource,
		r.reconcileQueueUnit,
		r.reconcileNotebook,
		r.reconcileCloudDiskImageSHA256,
		r.reconcileDataSourceForBilling,
	} {
		if result, err := reconcileFunc(ctx, instance); utils.HasErrorOrRequeue(result, err) {
			logger.Info("reconcile DswInstance, has error or need requeue", "err", err, "requeue", result.Requeue, "reconcileFunc", rt.FuncForPC(reflect.ValueOf(reconcileFunc).Pointer()).Name())
			return result, err
		}
	}

	// Reconcile instance fsm
	switch instance.Status.Status {
	case dswv1.Starting:
		return r.reconcileDswInstanceStarting(ctx, instance)
	case dswv1.Stopped:
		return r.reconcileDswInstanceStopped(ctx, instance)
	case dswv1.Stopping:
		return r.reconcileDswInstanceStopping(ctx, instance)
	case dswv1.Saving:
		return r.reconcileDswInstanceSaving(ctx, instance)
	case dswv1.Saved:
		return r.reconcileDswInstanceSaved(ctx, instance)
	case dswv1.Updating:
		return r.reconcileDswInstanceUpdating(ctx, instance)
	case dswv1.Recovering:
		return r.reconcileDswInstanceRecovering(ctx, instance)
	case dswv1.Queuing:
		return r.reconcileDswInstanceQueuing(ctx, instance)
	case dswv1.EnvPreparing:
		return r.reconcileDswInstanceEnvPreparing(ctx, instance)
	case dswv1.ResourceAllocating:
		return r.reconcileDswInstanceResourceAllocating(ctx, instance)
	case dswv1.Failed:
		return r.reconcileDswInstanceFailed(ctx, instance)
	case dswv1.SaveFailed:
		return r.reconcileDswInstanceSaveFailed(ctx, instance)
	case dswv1.Running:
		return r.reconcileDswInstanceRunning(ctx, instance)
	case "":
		// Update status to Starting by default
		return r.reconcileDswInstanceEmptyStatus(ctx, instance)
	default:
		return ctrl.Result{}, fmt.Errorf("unknown dsw instance status: %s", instance.Status.Status)
	}
}

func withStatusNeedToCheckTimeout(status string) bool {
	return status == dswv1.Starting || status == dswv1.EnvPreparing || status == dswv1.ResourceAllocating || status == dswv1.Queuing || status == dswv1.Updating || status == dswv1.Recovering
}

// SetupWithManager register events and filters for this CR
func (r *DswInstanceReconciler) SetupWithManager(mgr ctrl.Manager) error {
	// The `.metadata.name` field must be indexed by the manager, so that we will be able to lookup `DswInstance` by a referenced `Notebook` name.
	if err := mgr.GetFieldIndexer().IndexField(context.Background(), &dswv1.DswInstance{}, ".metadata.name", func(rawObj client.Object) []string {
		instance := rawObj.(*dswv1.DswInstance)
		return []string{instance.Name}
	}); err != nil {
		return err
	}

	var controllerBuilder grayrelease.ControllerBuilder
	if r.Config.EnableGrayRelease {
		controllerBuilder = grayrelease.NewWrapperControllerManagedBy(mgr).
			WithExtraOptions(grayrelease.WrapperOptions{})
	} else {
		controllerBuilder = grayrelease.NewStandardControllerManagedBy(mgr)
	}

	controllerBuilder = controllerBuilder.
		For(&dswv1.DswInstance{}).
		// The `Watches()` function is a controller-runtime API that takes:
		//  - A Kind (i.e. `Notebook`)
		//  - A mapping function that converts a `Notebook` object to a list of reconcile requests for `DswInstance`.
		// We have separated this out into a separate function.
		//  - A list of options for watching the `Notebooks`
		//   - In our case, we only want the watch to be triggered when the ResourceVersion of the Notebook is changed.
		Watches(
			&source.Kind{Type: &dswv1.Notebook{}},
			handler.EnqueueRequestsFromMapFunc(r.findObjectsForNotebook),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		).
		Watches(
			&source.Kind{Type: &dsv1.DataSource{}},
			handler.EnqueueRequestsFromMapFunc(r.findDswInstanceByInstanceIdLabels),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		).
		Watches(
			&source.Kind{Type: &rgv1.ResourceGroup{}},
			handler.EnqueueRequestsFromMapFunc(r.findObjectsByName),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		).
		WithOptions(controller.Options{
			MaxConcurrentReconciles: r.Config.MaxConcurrentReconciles,
			RecoverPanic:            true,
		})

	if featureUtil.DefaultFeatureGate.Enabled(features.EnableGlooGateway) {
		controllerBuilder = controllerBuilder.Watches(
			&source.Kind{Type: &gatewayv1.Gateway{}},
			handler.EnqueueRequestsFromMapFunc(r.findObjectsByName),
			builder.WithPredicates(predicate.ResourceVersionChangedPredicate{}),
		)
	}

	return controllerBuilder.Complete(r)
}

//
// helpers
//

func (r *DswInstanceReconciler) findObjectsForNotebook(notebook client.Object) []reconcile.Request {
	instances := &dswv1.DswInstanceList{}
	listOps := &client.ListOptions{
		FieldSelector: fields.OneTermEqualSelector(".metadata.name", notebook.GetName()),
	}
	if err := r.List(context.TODO(), instances, listOps); err != nil {
		return []reconcile.Request{}
	}

	requests := make([]reconcile.Request, len(instances.Items))
	for i, item := range instances.Items {
		requests[i] = reconcile.Request{
			NamespacedName: types.NamespacedName{
				Name:      item.GetName(),
				Namespace: utils.DswUserNamespace,
			},
		}
	}
	return requests
}

func getDswInstanceId(object client.Object) (string, bool) {
	if object.GetLabels() != nil {
		if instanceId, ok := object.GetLabels()[utils.LabelInstanceId]; ok {
			if strings.HasPrefix(instanceId, "dsw-") {
				return instanceId, true
			}
		}
	}
	return "", false
}

func (r *DswInstanceReconciler) findDswInstanceByInstanceIdLabels(object client.Object) []reconcile.Request {
	requests := make([]reconcile.Request, 0)
	dswInstanceId, ok := getDswInstanceId(object)
	if !ok {
		return requests
	}
	instance := &dswv1.DswInstance{}
	if err := r.Get(context.TODO(), types.NamespacedName{Name: dswInstanceId, Namespace: utils.DswUserNamespace}, instance); err != nil {
		return requests
	}
	requests = append(requests, reconcile.Request{
		NamespacedName: types.NamespacedName{
			Name:      dswInstanceId,
			Namespace: utils.DswUserNamespace,
		},
	})
	return requests
}

func (r *DswInstanceReconciler) findObjectsByName(object client.Object) []reconcile.Request {
	requests := make([]reconcile.Request, 0)
	instance := &dswv1.DswInstance{}
	if err := r.Get(context.TODO(), types.NamespacedName{Name: object.GetName(), Namespace: utils.DswUserNamespace}, instance); err != nil {
		return requests
	}
	requests = append(requests, reconcile.Request{
		NamespacedName: types.NamespacedName{
			Name:      object.GetName(),
			Namespace: utils.DswUserNamespace,
		},
	})
	return requests
}

func (r *DswInstanceReconciler) addFinalizer(ctx context.Context, instance *dswv1.DswInstance) error {
	instance.AddFinalizer(dswInstanceFinalizer)
	return r.Update(ctx, instance)
}

func (r *DswInstanceReconciler) checkOrCreateAcrAnnotations(instance *dswv1.DswInstance) error {
	if !r.Config.EnablePullAcrEEImage {
		return nil
	}

	patch := client.MergeFrom(instance.DeepCopy())

	if instance.Annotations != nil {
		if _, ok := instance.Annotations[utils.ACRAnnotationAcrEEInsts]; ok {
			return nil
		}
	} else {
		instance.Annotations = make(map[string]string)
	}
	logger := utils.GetLogger(r.Log, instance)
	instIDs, err := GetAcrInstancesFromDswInstance(instance, r.Config)
	if err != nil {
		logger.Error(err, "Ignore errors when calculating ACR instances")
		return nil
	}
	instance.Annotations[utils.ACRAnnotationAcrEEInsts] = strings.Join(instIDs, ",")
	if len(instIDs) > 0 {
		logger.Info("associated ACR-EE instances", "acrInstances", instIDs)
	}

	return r.Patch(context.TODO(), instance, patch)
}

func isTimeOut(timestamp *metav1.Time, timeOutInSeconds int64) bool {
	deadline := timestamp.Add(time.Second * time.Duration(timeOutInSeconds))
	now := metav1.Now()
	return now.After(deadline)
}

func (r *DswInstanceReconciler) deleteDswInstance(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	if err := r.Delete(context.TODO(), instance); err != nil {
		logger.Error(err, "Failed to delete dsw instance")
		return ctrl.Result{}, err
	}
	return utils.Requeue(utils.ReconcileInterval)
}

func (r *DswInstanceReconciler) getStatusTimeoutMinute(instance *dswv1.DswInstance) int64 {
	timeoutMinute := r.Config.CreateTimeOutInMinute
	if instance.Status.Status == dswv1.Queuing {
		if instance.Spec.TimeoutConfig != nil && instance.Spec.TimeoutConfig.QueueTimeoutMinutes != nil {
			timeoutMinute = *instance.Spec.TimeoutConfig.QueueTimeoutMinutes
		} else {
			timeoutMinute = r.Config.QueueTimeOutInMinute
		}
	} else if instance.Status.Status == dswv1.EnvPreparing {
		if instance.Spec.TimeoutConfig != nil && instance.Spec.TimeoutConfig.EnvPreparingTimeoutMinutes != nil {
			timeoutMinute = *instance.Spec.TimeoutConfig.EnvPreparingTimeoutMinutes
		}
	} else if instance.Status.Status == dswv1.Recovering {
		timeoutMinute = r.Config.RecoverTimeOutInMinute
	}
	return timeoutMinute
}

func (r *DswInstanceReconciler) getTimeoutRemainingTime(instance *dswv1.DswInstance) time.Duration {
	checkTime := instance.Status.SpecUpdateTime
	if instance.Status.StatusLastUpdateTime != nil {
		checkTime = instance.Status.StatusLastUpdateTime
	}
	timeoutMinute := r.getStatusTimeoutMinute(instance)
	timeoutRemainingTime := checkTime.Add(time.Minute * time.Duration(timeoutMinute)).Sub(time.Now())
	if timeoutRemainingTime <= 0 {
		timeoutRemainingTime = utils.ReconcileInterval * time.Second
	}
	return timeoutRemainingTime
}

func (r *DswInstanceReconciler) reconcileInstanceTimeout(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)

	// specUpdateTime is not set, just ignore
	specUpdateTime := instance.Status.SpecUpdateTime
	if specUpdateTime == nil {
		return ctrl.Result{}, nil
	}

	// Start to process instance timeout
	var message string
	checkTime := specUpdateTime
	if instance.Status.StatusLastUpdateTime != nil {
		checkTime = instance.Status.StatusLastUpdateTime
	}
	timeoutMinute := r.getStatusTimeoutMinute(instance)
	if !isTimeOut(checkTime, timeoutMinute*60) {
		return ctrl.Result{}, nil
	}
	logger = logger.WithValues("specUpdateTime", specUpdateTime, "statusLastUpdateTime", instance.Status.StatusLastUpdateTime, "checkTime", checkTime, "timeoutMinute", timeoutMinute)

	if instance.Status.Status == dswv1.Queuing {
		logger.Info("DswInstance queue timeout")
		message = "Resource queue timeout, lack of sufficient resources"
		r.EventRecorder.Event(instance, corev1.EventTypeWarning, "QueueTimeout",
			fmt.Sprintf("DswInstance queue timeout %s/%s", instance.Namespace, instance.Name))
	} else if instance.Status.Status == dswv1.Starting || instance.Status.Status == dswv1.ResourceAllocating || instance.Status.Status == dswv1.EnvPreparing {
		logger.Info("Creating DswInstance timeout", "status", instance.Status.Status)
		if instance.Status.Message != "" {
			message = fmt.Sprintf("Creating DswInstance timeout, pod event is %s", instance.Status.Message)
		} else {
			message = fmt.Sprintf("Creating DswInstance timeout")
		}
		r.EventRecorder.Event(instance, corev1.EventTypeWarning, "CreateTimeout",
			fmt.Sprintf("Create DswInstance timeout %s/%s", instance.Namespace, instance.Name))
	} else if instance.Status.Status == dswv1.Updating {
		logger.Info("Update DswInstance timeout", "status", instance.Status.Status)
		message = "Update DswInstance timeout"
		r.EventRecorder.Event(instance, corev1.EventTypeWarning, "UpdateTimeout",
			fmt.Sprintf("Update DswInstance timeout %s/%s", instance.Namespace, instance.Name))
	} else if instance.Status.Status == dswv1.Recovering {
		logger.Info("Recover DswInstance timeout", "status", instance.Status.Status)
		message = "Recover DswInstance timeout"
		r.EventRecorder.Event(instance, corev1.EventTypeWarning, "RecoverTimeout",
			fmt.Sprintf("Recover DswInstance timeout %s/%s", instance.Namespace, instance.Name))
	}

	if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
		Status:         dswv1.Failed,
		Message:        message,
		LastUpdateTime: &metav1.Time{Time: time.Now()},
	}); err != nil {
		logger.Error(err, "Failed to update DswInstance status")
		return ctrl.Result{}, err
	}

	return utils.Requeue(utils.ReconcileInterval)
}

func (r *DswInstanceReconciler) reconcileResourceGroup(instance *dswv1.DswInstance) (ctrl.Result, error) {
	// The resource group need to be created inflight such as POSTPAY resource group
	var err error

	resourceGroupName := utils.GetResourceGroupName(instance)
	resourceGroupNamespace := utils.GetResourceGroupNamespace(r.Config.TenantScopeKey)
	logger := utils.GetLogger(r.Log, instance).WithValues("resourceGroupNamespace", resourceGroupNamespace)

	var foundRG = &rgv1.ResourceGroup{}
	err = r.Get(context.TODO(), types.NamespacedName{Name: resourceGroupName, Namespace: resourceGroupNamespace}, foundRG)
	if err != nil && apierrs.IsNotFound(err) {
		additionalSG := mtsv1.SecurityGroup{}

		allowInternetAccess, ok := instance.GetAnnotations()[utils.AnnotationAllowInternetAccess]
		if ok && utils.IsNotBlank(allowInternetAccess) {
			additionalSG.EgressRules = []mtsv1.SecurityGroupRule{
				{
					Protocol:    mtsv1.ALL,
					Priority:    18,
					SubjectCIDR: "0.0.0.0/0",
					PortRange:   "-1/-1",
					Policy:      mtsv1.Drop,
				},
			}

			for _, cidr := range strings.Split(allowInternetAccess, ",") {
				if _, _, err := net.ParseCIDR(cidr); err != nil {
					logger.Info("Allowed cidr is invalid", "cidr", cidr)
					return ctrl.Result{}, err
				}

				additionalSG.EgressRules = append(additionalSG.EgressRules, mtsv1.SecurityGroupRule{
					Protocol:    mtsv1.ALL,
					Priority:    18,
					SubjectCIDR: cidr,
					PortRange:   "-1/-1",
					Policy:      mtsv1.Accept,
				})
			}

			logger.Info("Configured additional security group", "additionalSG", additionalSG)
		}

		logger.Info("Creating ResourceGroup")

		rgLabels := map[string]string{
			utils.LabelKeyMetricTenantId: instance.Spec.UserId,
		}
		if value, exists := instance.Labels[utilconstants.LabelCniType]; exists && value == utilconstants.TerwayCniType {
			rgLabels[rgv1.LabelEnableTerwayCNI] = stringUtil.TrueString
		}

		resourceGroup := &rgv1.ResourceGroup{
			ObjectMeta: metav1.ObjectMeta{
				Name:      resourceGroupName,
				Namespace: resourceGroupNamespace,
				Labels:    rgLabels,
			},
			Spec: rgv1.ResourceGroupSpec{
				UserId:     instance.Spec.UserId,
				ChargeType: instance.Spec.ChargeType,
				DLink:      instance.Spec.DLink,
				Resources: map[string]*rgv1.ResourceSpec{
					instance.Name: {
						EcsType:            instance.Spec.EcsType,
						SystemDiskCategory: instance.Spec.SystemDiskCategory,
						SystemDiskSize:     instance.Spec.SystemDiskSize,
						ChargeType:         instance.Spec.ChargeType,
						EcsImageId:         instance.Spec.EcsImageId,
						Count:              1,
						ECISpec:            instance.Spec.ECISpec,
					},
				},
				AdditionalSecurityGroups: &additionalSG,
			},
		}

		if resourceGroup.Annotations == nil {
			resourceGroup.Annotations = make(map[string]string)
		}

		resourceutil.SetDswEcsCustomTags(&resourceGroup.Annotations, instance.Spec.UserId)

		// Deny access to pod cidr for dsw post paid instances
		resourceGroup.Annotations[utils.AnnotationDenyAccessToPodCidr] = stringUtil.TrueString

		if instance.Annotations != nil {
			if annotation, ok := instance.Annotations[utils.AnnotationCreateRouteEntryInUserVPC]; ok && annotation != "" {
				resourceGroup.Annotations[utils.AnnotationCreateRouteEntryInUserVPC] = annotation
			}
		}

		if r.Config.EnablePullAcrEEImage {
			if acrInstIDs, ok := utils.GetFromMap(instance.Annotations, utils.ACRAnnotationAcrEEInsts); ok {
				resourceGroup.Annotations[utils.ACRAnnotationAcrEEInsts] = acrInstIDs
			}
		}

		if utils.IsEcsSpot(instance) {
			duration := "0"
			ecsDuration := instance.Annotations[spot.AnnotationEcsSpotDuration]
			if ecsDuration != "" {
				num, ecsErr := strconv.Atoi(ecsDuration)
				if ecsErr != nil {
					logger.Error(ecsErr, "ecsDuration format error")
				} else {
					duration = fmt.Sprintf("%dh", num)
				}
			}

			if instance.Annotations[spot.AnnotationEcsSpotStrategy] == string(rgv1.SpotStrategySpotWithPriceLimit) {
				if instance.Annotations[spot.AnnotationEcsSpotPriceLimit] != "" {
					priceHourLimit, parseErr := strconv.ParseFloat(instance.Annotations[spot.AnnotationEcsSpotPriceLimit], 64)
					if parseErr != nil {
						logger.Error(parseErr, "ecs spot price limit format error")
						return ctrl.Result{}, parseErr
					}
					tempPriceMinute := priceHourLimit / 60
					strValue := strconv.FormatFloat(tempPriceMinute, 'f', 6, 64)
					priceMinuteLimit, quantityErr := resource.ParseQuantity(strValue)
					if quantityErr != nil {
						return ctrl.Result{}, quantityErr
					}
					logger.Info("spot price trans", "priceHourLimit", priceHourLimit, "tempPriceMinute", tempPriceMinute, "priceMinuteLimit", priceMinuteLimit.String())
					resourceGroup.Spec.Resources[instance.Name].SpotSpec = &rgv1.SpotSpec{
						SpotProvider: rgv1.SpotProviderECS,
						Strategy:     rgv1.SpotStrategySpotWithPriceLimit,
						PriceLimit:   priceMinuteLimit,
						Duration:     duration,
					}
				} else {
					logger.Error(fmt.Errorf("AnnotationEcsSpotPriceLimit is null"), "AnnotationEcsSpotPriceLimit is null")
				}
			}
			if instance.Annotations[spot.AnnotationEcsSpotStrategy] == string(rgv1.SpotStrategySpotAsPriceGo) {
				resourceGroup.Spec.Resources[instance.Name].SpotSpec = &rgv1.SpotSpec{
					SpotProvider: rgv1.SpotProviderECS,
					Strategy:     rgv1.SpotStrategySpotAsPriceGo,
					Duration:     duration,
				}
			}
		}
		if utils.IsLingjunSpot(instance) {
			resourceGroup.Annotations[rgv1.AnnotationSkipCreateASIQuota] = stringUtil.TrueString
			resourceGroup.Annotations[rgv1.AnnotationSkipCreateLRN] = stringUtil.TrueString
			resourceGroup.Spec.Resources[instance.Name].Count = 1
			resourceGroup.Spec.Resources[instance.Name].LRNSpec = &rgv1.LRNSpec{
				Type: instance.Spec.EcsType,
			}
			for _, container := range instance.Spec.Template.Spec.Containers {
				if container.Name == utils.DswNotebookContainerName {
					limits := container.Resources.Limits
					if limits != nil {
						resourceGroup.Spec.Resources[instance.Name].LRNSpec.Resources = limits.DeepCopy()
					}
				}
			}
			if instance.Annotations[spot.AnnotationSpotDiscountLimit] != "" {
				discountLimit, quantityErr := resource.ParseQuantity(instance.Annotations[spot.AnnotationSpotDiscountLimit])
				if quantityErr != nil {
					return ctrl.Result{}, quantityErr
				}
				logger.Info("lingjun spot price discount trans", "discountLimit", discountLimit.String())
				resourceGroup.Spec.Resources[instance.Name].SpotSpec = &rgv1.SpotSpec{
					SpotProvider:  rgv1.SpotProviderLingjun,
					Strategy:      rgv1.SpotStrategySpotWithPriceLimit,
					DiscountLimit: discountLimit,
				}
			} else {
				spotErr := fmt.Errorf("AnnotationSpotDiscountLimit is null")
				logger.Error(spotErr, "AnnotationSpotDiscountLimit is null")
				return ctrl.Result{}, spotErr
			}
			resourceGroup.Spec.Configs = map[string]string{
				rgv1.ConfigsKeyRuntimeClassName: "rund",
				rgv1.ConfigsKeyEnableRDMA:       stringUtil.TrueString,
				rgv1.ConfigsKeyEnableNimitz:     stringUtil.TrueString,
			}
			if gpuType, exist := instance.Labels[spot.LabelGpuType]; exist {
				resourceGroup.Spec.Configs[rgv1.ConfigsKeyGPUCardModel] = gpuType
				if resourceutils.CheckIfGPUTypeIsPPU(gpuType) || resourceutils.CheckIfGPUTypeIsAMD(gpuType) {
					resourceGroup.Spec.Configs[rgv1.ConfigsKeyRdmaNicType] = rgv1.RDMANicTypeEIC
				}
			}
		}

		if err := r.Create(context.TODO(), resourceGroup); err != nil {
			if !apierrs.IsAlreadyExists(err) {
				logger.Error(err, "Create ResourceGroup error")
				return ctrl.Result{}, err
			} else {
				logger.Info("ResourceGroup already exists")
			}
		} else {
			logger.Info("Create ResourceGroup succeed")
			if utils.IsLingjunSpot(instance) {
				logger.Info("Create LingjunSpot ResourceGroup succeed", "instance", instance.Name)
				return r.reconcileLingjunSpotJobBids(instance)
			}
		}

		return utils.Requeue(utils.ReconcileInterval)
	} else if err != nil {
		return ctrl.Result{}, err
	}

	// Update TenantNamespace to DswInstance
	if (foundRG.Status.TenantNamespace != "" && instance.Status.TenantNamespace != foundRG.Status.TenantNamespace) || instance.Status.ResourceGroup == "" {
		if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
			TenantNamespace: foundRG.Status.TenantNamespace,
			ResourceGroup:   foundRG.Name,
			LastUpdateTime:  &metav1.Time{Time: time.Now()},
		}); err != nil {
			return ctrl.Result{}, err
		} else {
			return utils.Requeue(utils.ReconcileInterval)
		}
	}
	// reconcile updating spec case
	result, err := r.ReconcileResourceGroupForUpdating(foundRG, instance)
	if utils.HasErrorOrRequeue(result, err) {
		return result, err
	}

	if utils.IsLingjunSpot(instance) {
		return ctrl.Result{}, nil
	}

	if foundRG.Status.Status == rgv1.ResourceAllocateSucceed {
		tenantName := utils.GetTenantNamespace(instance)

		if utils.IsECIIndirectModelEnabled(instance) && instance.Status.TenantNamespace == "" {
			return utils.Requeue(utils.ReconcileInterval)
		}

		// update a fake EcsId and NodeName in DswInstance status if eci enabled
		if utils.IsECIEnabled(instance) {
			if instance.Status.EcsId == "" || instance.Status.NodeName == "" {
				update := utils.IsECIIndirectModelEnabled(instance) || (foundRG.Status.VResourceGroupStatus != nil && foundRG.Status.VResourceGroupStatus.Status == mtsv1.ResourceStatusReady)
				if update {
					if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
						EcsId:    "eci",
						NodeName: "virtual-kubelet",
					}); err != nil {
						return ctrl.Result{}, err
					}
				}
			}
			return ctrl.Result{}, nil
		} else {
			// update ecsId and nodeName in DswInstance status
			for _, mgStatus := range foundRG.Status.MachineGroupStatus {
				if mgStatus.Name == instance.Name {
					if len(mgStatus.InstanceList) < 1 {
						logger.Info("Unexpected resourceGroup result")
						return utils.Requeue(utils.ReconcileInterval)
					}

					ecsID := mgStatus.InstanceList[0]
					node, err := r.getNodeByEcsID(ecsID, tenantName)
					if err != nil {
						logger.Info("Wait until ecs to join ack cluster", "ecsId", ecsID)
						return utils.Requeue(utils.ReconcileInterval)
					}

					if !r.isNodeReady(instance, node) {
						logger.Info("Wait until node is ready", "ecsId", ecsID, "nodeName", node.Name)
						return utils.Requeue(utils.ReconcileInterval)
					}

					if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
						EcsId:    ecsID,
						NodeName: node.Name,
					}); err != nil {
						return ctrl.Result{}, err
					}

					return ctrl.Result{}, nil
				}
			}
		}
	}

	// If instance is in Running status, ignore checking the resource group status
	if instance.Status.Status != dswv1.Running {
		if foundRG.Status.Status == rgv1.ResourceAllocateFailed {
			if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
				Status:  dswv1.Failed,
				Message: foundRG.Status.Message,
			}); err != nil {
				return ctrl.Result{}, err
			}
		}

		if foundRG.Status.Status == rgv1.ResourceAllocating {
			if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
				Status:  dswv1.ResourceAllocating,
				Message: foundRG.Status.Message,
			}); err != nil {
				return ctrl.Result{}, err
			}
		}
	} else if utils.IsEcsSpot(instance) {
		//Sync notebook status to dswinstance when spot instance be stop
		var foundNotebook = &dswv1.Notebook{}
		if noteErr := r.Get(context.TODO(), types.NamespacedName{Name: instance.Name, Namespace: r.getNamespace(instance)}, foundNotebook); noteErr != nil {
			if apierrs.IsNotFound(err) {
				logger.Error(noteErr, "Check spot instance status when reconcileResourceGroup, but notebook not found")
			} else {
				logger.Error(noteErr, "Check spot instance status when reconcileResourceGroup, but get notebook error")
			}
		} else {
			if instance.Status.Status == dswv1.Running && foundNotebook.Status.Status == dswv1.Recovering {
				if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
					Status:      foundNotebook.Status.Status,
					Message:     foundNotebook.Status.Message,
					LastPodName: foundNotebook.Status.LastPodName,
				}); err != nil {
					return ctrl.Result{}, err
				}
			}
		}
	}

	// reconcile if ResourceGroup is not ready
	return utils.Requeue(utils.ReconcileInterval)
}

// ReconcileResourceGroupForUpdating reconcile the resource group to be the same with the spec
func (r *DswInstanceReconciler) ReconcileResourceGroupForUpdating(resourceGroup *rgv1.ResourceGroup, instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	// no resource group, no need to diff
	if resourceGroup == nil {
		logger.Info("ReconcileResourceGroupForUpdating instance without resource group")
		return ctrl.Result{}, nil
	}

	if utils.IsPrepaidInstance(instance) {
		logger.Info("ReconcileResourceGroupForUpdating prepaid dsw instance", "resourceGroupId", instance.Spec.ResourceGroupID)
		return ctrl.Result{}, nil
	}

	// resource group exists, check if changed or not
	resourceGroupSpecChanged := r.resourceGroupSpecChanged(resourceGroup, instance)
	spotSpecChanged := r.spotSpecChanged(resourceGroup, instance)
	if resourceGroupSpecChanged || spotSpecChanged {
		logger.Info("ReconcileResourceGroupForUpdating resource group is changed")
		if result, err := r.updateInstanceStatusToUpdatingWhenInstanceIsRunning(instance); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}

		// if resource group spec changed, need to delete Notebook CR.
		if result, err := r.deleteNotebook(instance); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}

		// if eci spec changed, means tenant namespace changed, need to delete all resources in tenant namespace.
		eciSpecChanged := r.eciSpecChanged(resourceGroup, instance)
		tenantNamespace := utils.GetNamespace(instance, r.Config)
		if eciSpecChanged {
			logger.Info("eci spec changed, need to delete all resource of tenant namespace", "tenantNamespace", tenantNamespace)

			if r.Config.WithMultiTenancy() {
				if result, err := r.deleteDswCredential(instance); utils.HasErrorOrRequeue(result, err) {
					return result, err
				}

				if result, err := r.deletePaiCredential(instance); utils.HasErrorOrRequeue(result, err) {
					return result, err
				}

				if result, err := r.deleteImagePullSecrets(instance); utils.HasErrorOrRequeue(result, err) {
					return result, err
				}
			}

			if result, err := r.deleteDswInstanceInfoConfigMap(instance); utils.HasErrorOrRequeue(result, err) {
				return result, err
			}

			if result, err := r.DataSourceService.delete(instance); utils.HasErrorOrRequeue(result, err) {
				logger.Error(err, "delete datasource of instance failed", "tenantNamespace", tenantNamespace)
				return result, err
			}
		} else {
			logger.Info("if tenant namespace is not changed, no need to delete resources in tenant namespace", "tenantNamespace", tenantNamespace)
		}

		if result, err := r.deleteQueueUnitIfExists(instance); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}

		if result, err := r.deleteLingjunSpotJobBids(instance); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}

		if err := r.Delete(context.TODO(), resourceGroup); err != nil {
			if apierrs.IsNotFound(err) {
				logger.Info("ResourceGroup is already deleted")
			} else {
				logger.Error(err, "Failed to delete Resource Group")
			}
			return ctrl.Result{}, err
		} else {
			logger.Info("ResourceGroup is deleted")
		}
		logger.Info("Resource Group is changed, wait until ResourceGroup is deleted")
		return utils.Requeue(utils.ReconcileInterval)
	}

	// resource exist and not changed
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) updateInstanceStatusToUpdatingWhenInstanceIsRunning(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	if utils.IsRunningInstance(instance) {
		status := &dswv1.DswInstanceStatus{
			Status:         dswv1.Updating,
			SpecUpdateTime: &metav1.Time{Time: time.Now()},
		}
		if result, err := r.updateStatus(instance, status); utils.HasErrorOrRequeue(result, err) {
			logger.Error(err, "update dsw instance status to Updating error")
			return result, err
		}
		return utils.Requeue(utils.ReconcileInterval)
	}
	logger.Info("UpdateInstanceStatusToUpdatingWhenInstanceIsRunning, current status is not Running, can not set to Updating", "status", instance.Status.Status)
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) reconcileNotebookForUpdating(notebook *dswv1.Notebook, instance *dswv1.DswInstance) (ctrl.Result, error) {
	if notebook == nil {
		return utils.Requeue(utils.ReconcileInterval)
	}

	logger := utils.GetLogger(r.Log, notebook)

	datasource, err := r.DataSourceService.getRootfsDatasource(instance.Name)
	if err != nil {
		return ctrl.Result{}, err
	}
	currentDts, err := r.DataSourceService.getCurrentDataSourceList(instance)
	if err != nil {
		return ctrl.Result{}, err
	}
	if result, err := r.patchDynamicLabels(notebook, instance); utils.HasErrorOrRequeue(result, err) {
		logger.Error(err, "patch dynamic labels error")
		return result, err
	}

	if r.Config.WithLight() {
		if result, err := r.patchAnnotations(notebook, instance); utils.HasErrorOrRequeue(result, err) {
			logger.Error(err, "patch annotations error")
			return result, err
		}
	}

	isNotebookSpecChanged, err := utils.NotebookSpecChanged(notebook, instance, r.Config.WithLight(), datasource, currentDts)
	if err != nil {
		logger.Info("ReconcileNotebookForUpdating: error occur when checking whether NotebookSpec is changed", "err", err)
		return ctrl.Result{}, err
	}

	if isNotebookSpecChanged {
		logger.Info("ReconcileNotebookForUpdating NotebookSpec is changed")
		if result, err := r.updateInstanceStatusToUpdatingWhenInstanceIsRunning(instance); err != nil || result.Requeue {
			return result, err
		}
		if err := r.Delete(context.TODO(), notebook); err != nil {
			if apierrs.IsNotFound(err) {
				logger.Info("Notebook is already deleted, need reconcile again")
			} else {
				logger.Error(err, "Fail to delete Notebook")
			}
			return ctrl.Result{}, err
		}
		return utils.Requeue(utils.ReconcileInterval)
	}

	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) patchAnnotations(notebook *dswv1.Notebook, instance *dswv1.DswInstance) (ctrl.Result, error) {
	if notebook == nil || instance == nil {
		return ctrl.Result{}, nil
	}

	logger := utils.GetLogger(r.Log, notebook)
	patch := client.MergeFrom(notebook.DeepCopy())

	needPatch := false
	if notebook.Annotations[utils.AnnotationDSWImageAuth] != instance.Annotations[utils.AnnotationDSWImageAuth] {
		notebook.Annotations[utils.AnnotationDSWImageAuth] = instance.Annotations[utils.AnnotationDSWImageAuth]
		needPatch = true
	}

	if needPatch {
		if err := r.Patch(context.TODO(), notebook, patch); err != nil {
			logger.Error(err, "Failed to patch notebook annotations")
			return ctrl.Result{}, err
		}
	}

	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) patchDynamicLabels(notebook *dswv1.Notebook, instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, notebook)
	patch := client.MergeFrom(notebook.DeepCopy())

	if notebook.Labels == nil {
		notebook.Labels = make(map[string]string)
	}
	if notebook.Labels == nil {
		notebook.Labels = make(map[string]string)
	}

	instanceLabelValue, instanceLabelExists := instance.Labels[utils.LabelKeyForwarderEnabled]
	notebookLabelValue, notebookLabelExists := notebook.Labels[utils.LabelKeyForwarderEnabled]

	needPatch := false
	if !instanceLabelExists && !notebookLabelExists {
		return ctrl.Result{}, nil
	} else if !instanceLabelExists && notebookLabelExists {
		delete(notebook.Labels, utils.LabelKeyForwarderEnabled)
		needPatch = true
	} else if instanceLabelExists && !notebookLabelExists {
		notebook.Labels[utils.LabelKeyForwarderEnabled] = notebookLabelValue
		needPatch = true
	} else {
		if notebookLabelValue != instanceLabelValue {
			notebook.Labels[utils.LabelKeyForwarderEnabled] = notebookLabelValue
			needPatch = true
		}
	}

	if needPatch {
		err := r.Patch(context.TODO(), notebook, patch)
		if err != nil {
			logger.Error(err, "Failed to patch pod")
			return ctrl.Result{}, err
		}
		logger.Info("Succeeded patching pod", "notebook", notebook.Name)
	}

	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) handleDswInstancePreemption(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)

	namespace := r.getNamespace(instance)
	namespacedName := types.NamespacedName{
		Name:      instance.Name,
		Namespace: namespace,
	}

	foundNotebook := &dswv1.Notebook{}
	if err := r.Get(context.TODO(), namespacedName, foundNotebook); err != nil {
		if !apierrs.IsNotFound(err) {
			return ctrl.Result{}, err
		}
		return ctrl.Result{}, nil
	}

	if v, ok := foundNotebook.Labels[utils.LabelKeyPreemption]; ok {
		if instance.Labels == nil {
			instance.Labels = make(map[string]string)
		}
		if _, statusCauseLabelExists := instance.Labels[utils.LabelKeyStatusCause]; !statusCauseLabelExists {
			patch := client.MergeFrom(instance.DeepCopy())
			if v == persistenceutil.PodSubStatusEvictedByDrain {
				instance.Labels[utils.LabelKeyStatusCause] = utils.NodeDrain
			} else if v == persistenceutil.OversoldPreemption {
				instance.Labels[utils.LabelKeyStatusCause] = utils.OversoldPreemption
			} else if v == spot.EciSpotPreemption {
				instance.Labels[utils.LabelKeyStatusCause] = utils.EciSpotPreemption
			} else if v == spot.LingjunSpotPreemption {
				instance.Labels[utils.LabelKeyStatusCause] = utils.LingjunSpotPreemption
			} else {
				instance.Labels[utils.LabelKeyStatusCause] = utils.Preempted
			}
			if err := r.Patch(context.TODO(), instance, patch); err != nil {
				return ctrl.Result{}, err
			}
		}
		logger.Info("Delete dsw instance because it was preempted", "preemption", v)
		return r.deleteDswInstance(instance)
	} else {
		return ctrl.Result{}, nil
	}
}

func (r *DswInstanceReconciler) resourceGroupSpecChanged(resourceGroup *rgv1.ResourceGroup, instance *dswv1.DswInstance) bool {
	// 1.check the ecsSpec, if ecsSpecs are different, delete the existing resource group, it will be creating again
	logger := utils.GetLogger(r.Log, instance)
	ecsSpecChanged := r.ecsSpecChanged(resourceGroup, instance)
	dlinkSpecChanged := r.dlinkSpecChanged(resourceGroup, instance)
	result := ecsSpecChanged || dlinkSpecChanged
	logger.Info("Check whether the resource group has been changed", "ecsSpecChanged", ecsSpecChanged, "dlinkSpecChanged", dlinkSpecChanged, "result", result)
	return result
}

func (r *DswInstanceReconciler) eciSpecChanged(resourceGroup *rgv1.ResourceGroup, instance *dswv1.DswInstance) bool {
	logger := utils.GetLogger(r.Log, instance)
	currentEciSpec := resourceGroup.Spec.Resources[instance.Name].ECISpec
	desiredEciSpec := instance.Spec.ECISpec

	currentIsEciEnabled := currentEciSpec != nil && currentEciSpec.Enable
	currentIsEciIndirectModelEnabled := currentIsEciEnabled && currentEciSpec.EnableIndirectMode

	desiredIsEciEnabled := desiredEciSpec != nil && desiredEciSpec.Enable
	desiredIsEciIndirectModelEnabled := desiredIsEciEnabled && desiredEciSpec.EnableIndirectMode

	eciSpecChanged := currentIsEciEnabled != desiredIsEciEnabled || currentIsEciIndirectModelEnabled != desiredIsEciIndirectModelEnabled

	if eciSpecChanged {
		logger.Info("eci spec changed", "currentIsEciEnabled", currentIsEciEnabled, "currentIsEciIndirectModelEnabled", currentIsEciIndirectModelEnabled, "desiredIsEciEnabled", desiredIsEciEnabled, "desiredIsEciIndirectModelEnabled", desiredIsEciIndirectModelEnabled)
	}
	return eciSpecChanged
}

func (r *DswInstanceReconciler) ecsSpecChanged(resourceGroup *rgv1.ResourceGroup, instance *dswv1.DswInstance) bool {
	logger := utils.GetLogger(r.Log, instance)
	currentEcsSpec := resourceGroup.Spec.Resources[instance.Name].EcsType
	desiredEcsSpec := instance.Spec.EcsType
	ecsSpecChanged := currentEcsSpec != desiredEcsSpec
	if ecsSpecChanged {
		logger.Info("ecsSpecChanged ecsSpec are different", "currentEcsSpec", currentEcsSpec, "desiredEcsSpec", desiredEcsSpec, "resourceGroup", resourceGroup.Name)
		return true
	}
	return r.eciSpecChanged(resourceGroup, instance)
}

func (r *DswInstanceReconciler) dlinkSpecChanged(resourceGroup *rgv1.ResourceGroup, instance *dswv1.DswInstance) bool {
	logger := utils.GetLogger(r.Log, instance)
	currentDlink := resourceGroup.Spec.DLink
	desiredDlink := instance.Spec.DLink
	if currentDlink == nil && desiredDlink == nil {
		return false
	} else if currentDlink != nil && desiredDlink != nil {
		currentVpcID := currentDlink.VpcID
		desiredVpcID := desiredDlink.VpcID
		vpcIDChanged := currentVpcID != desiredVpcID

		currentSwitchID := currentDlink.SwitchID
		desiredSwitchID := desiredDlink.SwitchID
		switchIDChanged := currentSwitchID != desiredSwitchID

		currentSecurityGroupID := currentDlink.SecurityGroupID
		desiredSecurityGroupID := desiredDlink.SecurityGroupID
		securityGroupIDChanged := currentSecurityGroupID != desiredSecurityGroupID

		currentDefaultRoute := currentDlink.DefaultRoute
		desiredDefaultRoute := desiredDlink.DefaultRoute
		defaultRouteChanged := currentDefaultRoute != desiredDefaultRoute

		dlinkChanged := vpcIDChanged || switchIDChanged || securityGroupIDChanged || defaultRouteChanged
		if dlinkChanged {
			logger.Info("dlinkSpecChanged dlinks are different", "currentVpcID", currentVpcID,
				"currentSecurityGroupID", currentSecurityGroupID, "desiredVpcID", desiredVpcID,
				"desiredSecurityGroupID", desiredSecurityGroupID, "resource group", resourceGroup.Name)
		}

		return dlinkChanged
	}
	return true
}

func (r *DswInstanceReconciler) reconcilePAIQuota(instance *dswv1.DswInstance) (ctrl.Result, error) {
	var found = &pqv1.PAIQuota{}
	err := r.Get(context.TODO(), types.NamespacedName{Name: instance.Spec.ResourceGroupID}, found)
	if err != nil && apierrs.IsNotFound(err) {
		if _, err = r.updateStatus(instance, &dswv1.DswInstanceStatus{
			Status:  dswv1.Failed,
			Message: fmt.Sprintf("The Quota %s is not ready", instance.Spec.ResourceGroupID),
		}); err != nil {
			return ctrl.Result{}, err
		}
	} else if err != nil {
		return ctrl.Result{}, err
	}

	// Update EcsId && NodeName to DswInstance
	if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
		EcsId:           found.Name,
		NodeName:        found.Name,
		TenantNamespace: utils.GetTenantNamespace(instance),
		ResourceGroup:   instance.Spec.ResourceGroupID,
		LastUpdateTime:  &metav1.Time{Time: time.Now()},
	}); err != nil {
		return ctrl.Result{}, err
	}

	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) getPriorityClassName(instance *dswv1.DswInstance) string {
	if r.Config.WithLight() {
		return fmt.Sprintf(
			"%s-%d", paiPriorityClassNamePrefix, instance.Spec.Priority)
	}

	return defaultPriorityClassName
}

// By watching whether the scheduling.x-k8s.io/suspend annotation on the DswInstance is deleted, determine whether the QueueUnit has been dequeued
func (r *DswInstanceReconciler) reconcileQueueUnitStatusBySchedulingSuspendAnnotation(instance *dswv1.DswInstance) (ctrl.Result, error) {
	// If the annotation still exists, it means  the instance has not been scheduled yet. At this time, the user can modify the priority of the QueueUnit.
	if _, ok := instance.Annotations[kubeAnnotationKeyOfSchedulingSuspend]; ok {
		logger := utils.GetLogger(r.Log, instance)
		logger.Info("DswInstance is queuing for scheduling suspend, annotation scheduling.x-k8s.io/suspend exists")

		if r.Config.WithLight() {
			namespace := r.getNamespace(instance)
			var foundQueueUnit = &queuev1.QueueUnit{}
			if err := r.Get(context.TODO(), types.NamespacedName{Name: instance.Name, Namespace: namespace}, foundQueueUnit); err != nil {
				return ctrl.Result{}, err
			}
			if utils.GetQueueUnitConsumerRefKind(foundQueueUnit) != dswv1.DswInstanceKind {
				return ctrl.Result{}, nil
			}

			if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
				Status: dswv1.Queuing,
			}); err != nil {
				return ctrl.Result{}, err
			}

			priorityChanged := instance.Spec.Priority != *foundQueueUnit.Spec.Priority
			statusChanged := instance.Status.Status != foundQueueUnit.Annotations[common.AnnotationPAIJobStatus]
			// If priority changed
			if priorityChanged {
				foundQueueUnit.Spec.Priority = &instance.Spec.Priority
				foundQueueUnit.Spec.PriorityClassName = r.getPriorityClassName(instance)

				r.EventRecorder.Event(instance,
					corev1.EventTypeNormal,
					"reconcileQueueUnit",
					fmt.Sprintf("Update DswInstance %s queue priority to %d", instance.Name, instance.Spec.Priority))
			}
			// If status changed
			if statusChanged {
				foundQueueUnit.Annotations[common.AnnotationPAIJobStatus] = instance.Status.Status
			}
			// Update queue unit if priority changed or status changed
			if priorityChanged || statusChanged {
				if err := r.Update(context.TODO(), foundQueueUnit); err != nil {
					return ctrl.Result{}, err
				}
			}
			return utils.Requeue(utils.ReconcileInterval)
		}
	}
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) reconcileQueueUnitForAsiCloudOrLingjun(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	if r.Config.WithAsiCloud() || r.Config.WithAsiLingjun() {
		logger.Info("DswInstance is queuing for asi cloud or lingjun")
		// Check queue unit status
		namespace := r.getNamespace(instance)
		var foundQueueUnit = &queuev1.QueueUnit{}
		if err := r.Get(context.TODO(), types.NamespacedName{Name: instance.Name, Namespace: namespace}, foundQueueUnit); err != nil {
			if apierrs.IsNotFound(err) {
				logger.Info("reconcileQueueUnitForAsiCloudOrLingjun, QueueUnit not found, need requeue")
				return utils.Requeue(utils.ReconcileShortInterval)
			}
			return ctrl.Result{}, err
		}
		if utils.GetQueueUnitConsumerRefKind(foundQueueUnit) != dswv1.DswInstanceKind {
			return ctrl.Result{}, nil
		}

		priorityChanged := false
		if foundQueueUnit.Status.Phase != constants.StatusDequeued {
			logger.Info("DswInstance is Queueing", "QUStatus", foundQueueUnit.Status)

			if _, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
				Status: dswv1.Queuing,
			}); err != nil {
				return ctrl.Result{}, err
			}
			if foundQueueUnit.Spec.Priority == nil || instance.Spec.Priority != *foundQueueUnit.Spec.Priority {
				priorityChanged = true
				foundQueueUnit.Spec.Priority = &instance.Spec.Priority
				foundQueueUnit.Spec.PriorityClassName = r.getPriorityClassName(instance)
			}
		}

		statusChanged := instance.Status.Status != foundQueueUnit.Annotations[common.AnnotationPAIJobStatus]
		// If status changed
		if statusChanged {
			foundQueueUnit.Annotations[common.AnnotationPAIJobStatus] = instance.Status.Status
		}
		// Update queue unit if priority changed or status changed
		if priorityChanged || statusChanged {
			logger.Info("need to update queue unit priority or status", "priorityChanged", priorityChanged, "statusChanged", statusChanged)
			if err := r.Update(context.TODO(), foundQueueUnit); err != nil {
				return ctrl.Result{}, err
			}
			if priorityChanged {
				r.EventRecorder.Event(instance, corev1.EventTypeNormal, "reconcileQueueUnit",
					fmt.Sprintf("Update DswInstance %s queue priority to %d", instance.Name, instance.Spec.Priority))
			}
		}
		if foundQueueUnit.Status.Phase != constants.StatusDequeued {
			return utils.Requeue(utils.ReconcileInterval)
		}
	}
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) reconcileQueueUnit(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	if r.Config.EnableAsiQuota && instance.Spec.ResourceGroupID == "" && !utils.IsLingjunSpot(instance) {
		return ctrl.Result{}, nil
	}
	// In a lightweight environment, QueueUnit is still created by the notebook controller
	// TODO Delete this judgment logic after kubeDL is upgraded in lightweight
	if r.Config.WithLight() {
		return ctrl.Result{}, nil
	}
	// If quota id is not set in DswInstance in light environment, return
	if r.Config.WithLight() && instance.Spec.QuotaId == "" {
		return ctrl.Result{}, nil
	}
	// try to figure out if this notebook is scheduled by queue unit
	if r.Config.JobSchedulePolicy != jobSchedulePolicyQueueUnit {
		logger.Info("Ignore creating QueueUnit", "jobSchedulePolicy", r.Config.JobSchedulePolicy)
		return ctrl.Result{}, nil
	}

	if result, err := r.createOrUpdateQueueUnit(instance); utils.HasErrorOrRequeue(result, err) {
		return result, err
	}
	// Wait for queuing
	if result, err := r.reconcileQueueUnitStatusBySchedulingSuspendAnnotation(instance); utils.HasErrorOrRequeue(result, err) {
		return result, err
	}
	return r.reconcileQueueUnitForAsiCloudOrLingjun(instance)
}

func (r *DswInstanceReconciler) createOrUpdateQueueUnit(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	// find the annotation and try to get QueueUnit
	logger.Info("Start to create QueueUnit by dsw controller", "instance", instance.Name)

	namespace := r.getNamespace(instance)
	var foundQueueUnit = &queuev1.QueueUnit{}
	if err := r.Get(context.TODO(), types.NamespacedName{Name: instance.Name, Namespace: namespace}, foundQueueUnit); err != nil {
		if apierrs.IsNotFound(err) {
			if result, err := r.addSchedulingSuspendAnnotationIfNotExists(instance); utils.HasErrorOrRequeue(result, err) {
				return result, err
			}
			// 2.1 there is no specified QueueUnit in k8s
			logger.Info("Creating QueueUnit by dsw controller", "instance", instance.Name)

			// 2.2 generate a new QueueUnit
			queueUnit, err := r.generateQueueUnit(instance)
			if err != nil {
				return ctrl.Result{}, err
			}

			// 2.3 create the QueueUnit
			err = r.Create(context.TODO(), queueUnit)
			if err != nil {
				return ctrl.Result{}, err
			}
			logger.Info("Create QueueUnit by dsw controller succeed", "instance", instance.Name)
			if result, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{Status: dswv1.Queuing}); utils.HasErrorOrRequeue(result, err) {
				return result, err
			}

			// 2.4 create corresponding Event to inform
			r.EventRecorder.Event(instance, corev1.EventTypeNormal, "reconcileQueueUnit", fmt.Sprintf("Created QueueUnit %s/%s", namespace, instance.Name))
			return utils.Requeue(utils.ReconcileShortInterval)
		} else {
			return ctrl.Result{}, err
		}
	} else {
		if result, err := r.reconcileQueueUnitForUpdating(instance, foundQueueUnit); utils.HasErrorOrRequeue(result, err) {
			return result, nil
		}
	}
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) addSchedulingSuspendAnnotationIfNotExists(instance *dswv1.DswInstance) (ctrl.Result, error) {
	// Add scheduling.x-k8s.io/suspend annotation to DswInstance if not exists.
	// TODO Add annotation by dsw service when create or update DswInstance CR.
	if !r.Config.WithLight() {
		return ctrl.Result{}, nil
	}
	if _, ok := instance.Annotations[kubeAnnotationKeyOfSchedulingSuspend]; !ok {
		instance.Annotations[kubeAnnotationKeyOfSchedulingSuspend] = stringUtil.TrueString
		if err := r.Update(context.TODO(), instance); err != nil {
			return ctrl.Result{}, err
		}
		return utils.Requeue(utils.ReconcileShortInterval)
	}
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) isDswInstanceResourceChanged(instance *dswv1.DswInstance, foundQueueUnit *queuev1.QueueUnit) (bool, error) {
	logger := utils.GetLogger(r.Log, instance)

	mainContainerRequests := instance.Spec.Template.Spec.Containers[0].Resources.Requests
	desiredQueueUnitResource, err := r.generateQueueUnitResource(instance)
	if err != nil {
		return false, err
	}

	currentQueueUnitResource := foundQueueUnit.Spec.Resource.DeepCopy()

	// Refer to the logic of judging whether the Notebook resources have changed in the ContainerResourcesChanged function and delete these two keys.
	desiredQueueUnitResourceToCmp := utils.FilterResourceKey(desiredQueueUnitResource, queueUnitCompareResourceKeys)
	currentQueueUnitResourceToCmp := utils.FilterResourceKey(currentQueueUnitResource, queueUnitCompareResourceKeys)

	isDswInstanceResourceChanged := !reflect.DeepEqual(desiredQueueUnitResourceToCmp, currentQueueUnitResourceToCmp)

	if isDswInstanceResourceChanged {
		logger.Info("Resource of DswInstance changed", "mainContainerRequests", mainContainerRequests,
			"desiredQueueUnitResource", desiredQueueUnitResource, "currentQueueUnitResource", currentQueueUnitResource,
			"desiredQueueUnitResourceToCmp", desiredQueueUnitResourceToCmp, "currentQueueUnitResourceToCmp", currentQueueUnitResourceToCmp)
	}

	return isDswInstanceResourceChanged, nil
}

func (r *DswInstanceReconciler) reconcileQueueUnitForUpdating(instance *dswv1.DswInstance, foundQueueUnit *queuev1.QueueUnit) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)

	if utils.GetQueueUnitConsumerRefKind(foundQueueUnit) != dswv1.DswInstanceKind {
		return ctrl.Result{}, nil
	}
	isDswInstanceResourceChanged, err := r.isDswInstanceResourceChanged(instance, foundQueueUnit)
	if err != nil {
		return ctrl.Result{}, err
	}
	if isDswInstanceResourceChanged {
		logger.Info("ReconcileQueueUnitForUpdating isDswInstanceResourceChanged is changed")
		if result, err := r.updateInstanceStatusToUpdatingWhenInstanceIsRunning(instance); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}
		if result, err := r.deleteNotebook(instance); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}

		if err := r.Delete(context.TODO(), foundQueueUnit); err != nil {
			if apierrs.IsNotFound(err) {
				logger.Info("QueueUnit is already deleted")
			} else {
				logger.Error(err, "Failed to delete QueueUnit")
			}
			return ctrl.Result{}, err
		}
		logger.Info("QueueUnit is changed, wait until ResourceGroup is deleted")
		return utils.Requeue(utils.ReconcileInterval)
	}
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) generateObjectReference(instance *dswv1.DswInstance) *corev1.ObjectReference {
	return &corev1.ObjectReference{
		APIVersion: dswv1.GroupVersion.Identifier(),
		Kind:       dswv1.DswInstanceKind,
		Namespace:  instance.Namespace,
		Name:       instance.Name,
	}
}

func (r *DswInstanceReconciler) updateQueueUnitKoordinatorGpuResource(mainContainerRequest corev1.ResourceList, queueUnitResource corev1.ResourceList) {
	if r.Config.WithAsiLingjun() {
		var gpuRatio *resource.Quantity
		if gpuMemoryRatio, ok := mainContainerRequest[common.AlibabaCloudGpuMemoryRatio]; ok {
			gpuRatio = &gpuMemoryRatio
		} else if gpuMemoryRatio, ok = mainContainerRequest[common.KoordinatorGpuMemoryRatio]; ok {
			gpuRatio = &gpuMemoryRatio
		}
		if gpuRatio != nil {
			// Set gpu memory ratio accordingly
			queueUnitResource[common.KoordinatorGpuMemoryRatio] = *gpuRatio

			// Note: Currently we set gpu core ratio to the same as gpu memory ratio
			// Set gpu core ratio to the same as gpu memory ratio
			queueUnitResource[common.KoordinatorGpuCore] = *gpuRatio

			// Set to enable gpu share
			gpuShareQuantity, _ := resource.ParseQuantity("1")
			queueUnitResource[common.KoordinatorGpuShared] = gpuShareQuantity

			// Remove AlibabaCloudGpuMemoryRatio since this should only be use in ECS clusters
			delete(queueUnitResource, common.AlibabaCloudGpuMemoryRatio)
		}
	}
}

func (r *DswInstanceReconciler) updateQueueUnitAliyunEniResource(mainContainerRequest corev1.ResourceList, queueUnitResource corev1.ResourceList) {
	if r.Config.WithAsiLingjun() {
		if r.Config.EnableAliyunEniResource {
			eniQuantity, err := resource.ParseQuantity(strconv.Itoa(common.DefaultAliyunResourceEniCount))
			if err != nil {
				return
			}
			queueUnitResource[common.AliyunResourceEni] = eniQuantity
		}
	}
}

func (r *DswInstanceReconciler) updateQueueUnitEphemeralStorage(mainContainerRequest corev1.ResourceList, queueUnitResource corev1.ResourceList) {
	if r.Config.WithAsiLingjun() {
		minEphemeralStorage, ephemeralStorage := utils.GenerateEphemeralStorage(mainContainerRequest)
		cmpResult := ephemeralStorage.Cmp(*minEphemeralStorage)
		if cmpResult >= 0 {
			queueUnitResource[corev1.ResourceEphemeralStorage] = *ephemeralStorage
		} else {
			queueUnitResource[corev1.ResourceEphemeralStorage] = *minEphemeralStorage
		}
	}
}

func (r *DswInstanceReconciler) generateQueueUnitResource(instance *dswv1.DswInstance) (corev1.ResourceList, error) {
	dswNotebookContainer := utils.GetDswNotebookContainer(instance)
	if dswNotebookContainer == nil {
		return nil, fmt.Errorf("cannot find dsw-notebook dswNotebookContainer")
	}
	mainContainerRequest := dswNotebookContainer.Resources.Requests
	queueUnitResource := mainContainerRequest.DeepCopy()

	r.updateQueueUnitEphemeralStorage(mainContainerRequest, queueUnitResource)
	r.updateQueueUnitAliyunEniResource(mainContainerRequest, queueUnitResource)
	r.updateQueueUnitKoordinatorGpuResource(mainContainerRequest, queueUnitResource)

	return queueUnitResource, nil
}

func (r *DswInstanceReconciler) generateQueueUnit(instance *dswv1.DswInstance) (*queuev1.QueueUnit, error) {
	logger := utils.GetLogger(r.Log, instance)
	// 1. build ObjectReference from corresponding Job CR
	objectReference := r.generateObjectReference(instance)

	// 2. calculate the total resources of this queue unit
	queueUnitResource, err := r.generateQueueUnitResource(instance)
	if err != nil {
		return nil, err
	}
	logger.Info("generateQueueUnitResource succeed", "queueUnitResource", queueUnitResource)

	labels := map[string]string{}
	annotations := map[string]string{}
	// Set quota label if asi quota is enabled
	if r.Config.EnableAsiQuota {
		labels[asiQuotaNameKey] = instance.Labels[asiQuotaNameKey]
	}
	// Copy ali/metric- labels from notebook to queue unit
	// Copy quota resource limit config key from notebook to queue unit
	for labelKey, labelValue := range instance.Labels {
		if strings.HasPrefix(labelKey, utils.LabelKeyPrefix4Metrics) || labelKey == utils.LabelKey4ResourceLimit {
			labels[labelKey] = labelValue
		}
	}

	// Set annotations from notebook to queue unit
	if instance.Annotations != nil {
		annotations[common.AnnotationPAIJobDisplayName] = instance.Annotations[common.AnnotationPAIJobDisplayName]
		annotations[common.AnnotationPAIJobCreateTime] = instance.Annotations[common.AnnotationPAIJobCreateTime]
		annotations[common.AnnotationPAIJobStatus] = instance.Status.Status
		if instance.Annotations[utils.AnnotationOversoldType] != "" {
			annotations[utils.AnnotationQuotaOversoldType] = instance.Annotations[utils.AnnotationOversoldType]
		}
	}

	if utils.IsLingjunSpot(instance) {
		annotations[v1.AnnotationQueueSuspend] = stringUtil.TrueString
	}
	// set pod template annotation
	if podTemplate, err := r.generatePodTemplateAnnotation(instance); err != nil {
		return nil, err
	} else {
		annotations[utils.DswAnnotationPodTemplate] = podTemplate
	}

	// 3. build QueueUnit
	priority, err := strconv.ParseInt(instance.Labels[paiPriorityClassNameKey], 10, 32)
	if err != nil {
		logger.Error(err, "Fail to convert priority to int", "priority", priority)
	}
	priority32 := int32(priority)
	qu := &queuev1.QueueUnit{
		ObjectMeta: metav1.ObjectMeta{
			Name:        instance.Name,
			Namespace:   r.getNamespace(instance),
			Labels:      labels,
			Annotations: annotations,
		},
		Spec: queuev1.QueueUnitSpec{
			ConsumerRef:       objectReference,
			Priority:          &priority32,
			PriorityClassName: defaultPriorityClassName,
			Resource:          queueUnitResource,
		},
		Status: queuev1.QueueUnitStatus{
			Phase:   queuev1.Enqueued,
			Message: "the queue unit is enqueued after created",
		},
	}
	if r.Config.EnableAsiQuota {
		qu.Spec.Queue = instance.Labels[asiQuotaNameKey]
	}

	return qu, nil
}

func (r *DswInstanceReconciler) reconcileResource(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	if r.Config.WithMultiTenancy() && !paivnode.IsPaiVNodeWorkload(instance, utils.IsPaiVnodeInstanceEnabled()) {
		if instance.Spec.ResourceGroupID == "" {
			return r.reconcileResourceGroup(instance)
		} else {
			if result, err := r.reconcilePAIQuota(instance); utils.HasErrorOrRequeue(result, err) {
				logger.Error(err, "Fail to reconcile resource group")
				return result, err
			}
		}
	}
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) deleteResourceGroup(instance *dswv1.DswInstance) (ctrl.Result, error) {
	if utils.IsPrepaidInstance(instance) {
		return ctrl.Result{}, nil
	}

	var found = &rgv1.ResourceGroup{}
	resourceGroupName := utils.GetResourceGroupName(instance)
	resourceGroupNamespace := utils.GetResourceGroupNamespace(r.Config.TenantScopeKey)
	logger := utils.GetLogger(r.Log, instance).WithValues("resourceGroupNamespace", resourceGroupNamespace, "resourceGroup", resourceGroupName)

	if err := r.Get(context.TODO(), types.NamespacedName{Name: resourceGroupName, Namespace: resourceGroupNamespace}, found); err != nil {
		if apierrs.IsNotFound(err) {
			logger.Info("ResourceGroup is deleted")
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}

	if !found.IsBeingDeleted() {
		if err := r.Delete(context.TODO(), found); err != nil {
			logger.Error(err, "Failed to delete Resource Group")
		}
	}

	// Wait until resource group deleted
	logger.Info("Wait until ResourceGroup is deleted")
	return utils.Requeue(utils.ReconcileInterval)
}

func (r *DswInstanceReconciler) deleteQueueUnitIfExists(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	var foundQueueUnit = &queuev1.QueueUnit{}
	namespace := r.getNamespace(instance)
	if err := r.Get(context.TODO(), types.NamespacedName{Name: instance.Name, Namespace: namespace}, foundQueueUnit); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}
	if err := r.Delete(context.TODO(), foundQueueUnit); err != nil {
		if apierrs.IsNotFound(err) {
			logger.Info("QueueUnit is already deleted")
		} else {
			logger.Error(err, "Failed to delete QueueUnit")
		}
		return ctrl.Result{}, err
	}
	// Wait until resource group deleted
	logger.Info("Wait until QueueUnit is deleted")
	return utils.Requeue(utils.ReconcileInterval)
}

func (r *DswInstanceReconciler) reconcileImagePullSecrets(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	if instance == nil {
		return ctrl.Result{}, nil
	}

	logger := utils.GetLogger(r.Log, instance)

	var namespace string
	var secretManager SecretManager
	if !r.Config.WithLight() {
		secretManager = NewAcrSecretManager(r.Client, r.Config, logger)
		namespace = utils.GetTenantNamespace(instance)
		userTempSecretName := utils.GetUserTempSecretName(instance.Name)
		if result, err := secretManager.reconcileImagePullSecret(
			instance, instance.Spec.UserId, namespace, userTempSecretName); err != nil {
			return result, err
		}
	} else {
		namespace = instance.Spec.QuotaId
		secretManager = NewPrivateImageSecretManager(r.Client, r.Config, logger)
	}

	// vnode pod is running in cluster belonging to user ram account, so it should not create acr secret for pai repo
	if !paivnode.IsPaiVNodeWorkload(instance, utils.IsPaiVnodeInstanceEnabled()) {
		sysTempSecretName := utils.GetSysTempSecretName(instance.Name)
		if result, err := secretManager.reconcileImagePullSecret(
			instance, r.Config.ResourceAccountId, namespace, sysTempSecretName); err != nil {
			return result, err
		}
	}

	if r.Config.EnablePullAcrEEImage {
		acrEEUserTempSecretName := utils.GetAcrEEUserTempSecretName(instance.Name)
		acrEESecretManager := NewAcrEESecretManager(r.Client, r.Config, logger)
		if result, err := acrEESecretManager.reconcileImagePullSecret(
			instance, instance.Spec.UserId, namespace, acrEEUserTempSecretName); err != nil {
			return result, err
		}

		// vnode pod is running in cluster belonging to user ram account, so it should not create acr secret for pai repo
		if !paivnode.IsPaiVNodeWorkload(instance, utils.IsPaiVnodeInstanceEnabled()) {
			acrEESysTempSecretName := utils.GetAcrEESysTempSecretName(instance.Name)
			if result, err := acrEESecretManager.reconcileImagePullSecret(
				instance, r.Config.ResourceAccountId, namespace, acrEESysTempSecretName); err != nil {
				return result, err
			}
		}
	}
	if utils.IsPrivateImageEnabled(instance) {
		privateImageSecretName := utils.GetPrivateImageSecretName(instance.Name)
		privateImageSecretManager := NewPrivateImageSecretManager(r.Client, r.Config, logger)
		if result, err := privateImageSecretManager.reconcileImagePullSecret(
			instance, instance.Spec.UserId, namespace, privateImageSecretName); err != nil {
			return result, err
		}
	}
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) deleteImagePullSecrets(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	names := []string{
		utils.GetUserTempSecretName(instance.Name),
		utils.GetSysTempSecretName(instance.Name),
	}
	if r.Config.EnablePullAcrEEImage {
		names = append(names, utils.GetAcrEEUserTempSecretName(instance.Name))
		names = append(names, utils.GetAcrEESysTempSecretName(instance.Name))
	}
	if utils.IsPrivateImageEnabled(instance) {
		names = append(names, utils.GetPrivateImageSecretName(instance.Name))
	}
	namespace := utils.GetTenantNamespace(instance)
	if r.Config.WithLight() {
		namespace = instance.Spec.QuotaId
	}

	isDeleted := true
	var foundErr error = nil
	for _, name := range names {
		foundSecret := &corev1.Secret{}
		err := r.Get(context.TODO(), types.NamespacedName{Name: name, Namespace: namespace}, foundSecret)
		if err != nil && apierrs.IsNotFound(err) {
			continue
		}
		if err != nil {
			logger.Error(err, "Failed to get ImagePullSecret", "name", name, "namespace", namespace)
			foundErr = err
			continue
		}
		isDeleted = false
		if err := r.Delete(context.TODO(), foundSecret); err != nil {
			logger.Error(err, "Failed to delete ImagePullSecret", "name", name)
		}
	}
	if foundErr != nil {
		return ctrl.Result{}, foundErr
	}
	if isDeleted {
		return ctrl.Result{}, nil
	}

	// Wait until image pull secret deleted
	logger.Info("Wait until image pull secret is deleted", "names", names, "namespace", namespace)
	return utils.Requeue(utils.ReconcileInterval)
}

func (r *DswInstanceReconciler) getNodeByEcsID(ecsID string, tenantName string) (*corev1.Node, error) {
	var nodeList = &corev1.NodeList{}
	err := r.List(context.TODO(), nodeList, client.MatchingLabels{
		r.Config.TenantScopeKey: tenantName,
	})
	if err != nil {
		return nil, err
	}

	for _, node := range nodeList.Items {
		if ecsID != "" && strings.Contains(node.Spec.ProviderID, ecsID) {
			return &node, nil
		}
	}

	return nil, apierrs.NewNotFound(corev1.Resource("node"), ecsID)
}

func (r *DswInstanceReconciler) isNodeReady(instance *dswv1.DswInstance, node *corev1.Node) bool {
	// Check the node status is Ready from conditions
	conditionReady := false
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionTrue {
			conditionReady = true
			break
		}
	}

	if !conditionReady {
		return false
	}

	logger := utils.GetLogger(r.Log, instance)

	// There should only be one taint on Node
	for _, taint := range node.Spec.Taints {
		if r.Config.WithAsiCloud() {
			if taint.Key == "sigma.ali/resource-pool" {
				continue
			}
		}

		if taint.Key != r.Config.TenantScopeKey {
			logger.Info("Wait until taints ready", "node", node.Name, "taint", taint.Key)
			return false
		}
	}

	// Check gpu count matches
	if gpuCountStr, ok := node.Labels["aliyun.accelerator/nvidia_count"]; ok {
		gpuCapacity := node.Status.Capacity[constants.ResourceGPU]
		gpuCount := resource.MustParse(gpuCountStr)
		if gpuCapacity != gpuCount {
			logger.Info("Wait until gpu allocatable", "node", node.Name, "capacity", node.Status.Capacity)
			return false
		}
	}

	return true
}

func (r *DswInstanceReconciler) generateVolumesAndVolumeMounts(
	instance *dswv1.DswInstance, shmMem resource.Quantity) ([]corev1.Volume, []corev1.VolumeMount) {

	volumes := []corev1.Volume{
		{
			Name: shmVolumeName,
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{
					Medium:    corev1.StorageMediumMemory,
					SizeLimit: &shmMem,
				},
			},
		},
	}
	if instance.Spec.EnableMountCredential {
		volumes = append(volumes, corev1.Volume{
			Name: credentialVolumeName,
			VolumeSource: corev1.VolumeSource{
				Secret: &corev1.SecretVolumeSource{
					SecretName: instance.Name,
				},
			},
		})
	}

	volumeMounts := []corev1.VolumeMount{
		{
			Name:      shmVolumeName,
			MountPath: shmVolumeMountPath,
		},
	}
	// Mount credential configMap
	if instance.Spec.EnableMountCredential {
		volumeMounts = append(volumeMounts, corev1.VolumeMount{
			Name:      credentialVolumeName,
			MountPath: credentialVolumeMountPath,
			ReadOnly:  true,
		})
	}

	return volumes, volumeMounts
}

func (r *DswInstanceReconciler) generatePodTemplateAnnotation(instance *dswv1.DswInstance) (string, error) {
	logger := utils.GetLogger(r.Log, instance)
	notebook, err := r.generateNotebook(instance)
	if err != nil {
		logger.Error(err, "Failed to generate notebook.")
		return "", err
	} else {
		podTemplate := corev1.PodTemplateSpec{
			Spec: notebook.Spec.Template.Spec,
		}
		if podTemplateStr, err := json.Marshal(podTemplate); err != nil {
			logger.Error(err, "Failed to generate pod template.")
			return "", err
		} else {
			return string(podTemplateStr), nil
		}
	}
}

func (r *DswInstanceReconciler) generateNotebook(instance *dswv1.DswInstance) (*dswv1.Notebook, error) {
	var notebookFactory notebookFactory
	if r.Config.WithAsiInner() || r.Config.WithLight() {
		notebookFactory = &PlainNotebookFactory{r}
	} else if (r.Config.WithAsiCloud() || r.Config.WithAsiLingjun()) && instance.Spec.ResourceGroupID != "" || paivnode.IsPaiVNodeWorkload(instance, utils.IsPaiVnodeInstanceEnabled()) {
		notebookFactory = &AsiCloudPlainNotebookFactory{r}
	} else if r.Config.WithMultiTenancy() {
		notebookFactory = &TenantNotebookFactory{r}
	} else {
		return nil, fmt.Errorf("unknown clusterType %s", r.Config.ClusterType)
	}
	if utils.IsLingjunSpot(instance) {
		notebookFactory = &AsiCloudPlainNotebookFactory{r}
	}

	notebook, err := notebookFactory.generate(instance)
	if err != nil {
		return nil, err
	}
	return notebook, nil
}

func (r *DswInstanceReconciler) reconcileCredential(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	var err error
	logger := utils.GetLogger(r.Log, instance).WithName("reconcileCredential")
	// reconcile dswv1.Credential
	if instance.Spec.EnableMountCredential {
		tenantName := utils.GetTenantNamespace(instance)
		var foundCredential = &dswv1.Credential{}
		if err = r.Get(context.TODO(), types.NamespacedName{Name: instance.Name, Namespace: tenantName}, foundCredential); err != nil && apierrs.IsNotFound(err) {
			Credential := &dswv1.Credential{
				ObjectMeta: metav1.ObjectMeta{
					Name:      fmt.Sprintf("%s-credential", instance.Name),
					Namespace: tenantName,
				},
				Spec: dswv1.CredentialSpec{
					UserId:          instance.Spec.UserId,
					RoleArn:         instance.Spec.RoleArn,
					RefreshInterval: dswCredentialRefreshIntervalInSeconds,
					StsConfig:       instance.Spec.StsConfig,
				},
			}

			err = r.Create(context.TODO(), Credential)
			if err != nil {
				return ctrl.Result{}, err
			}

			r.EventRecorder.Event(instance, corev1.EventTypeNormal, "CreateCredential", fmt.Sprintf("Created Credential %s/%s", tenantName, instance.Name))
		}
	}

	// manage credentialv1.Credential
	if !r.Config.EnablePaiCredential {
		return ctrl.Result{}, nil
	}

	ownerInstanceInfo, err := paicredential.BuildCredentialOwnerInstanceInfo(&instance.ObjectMeta, instance.Spec.UserId, utils.GetTenantNamespace(instance))
	if err != nil {
		return ctrl.Result{}, err
	}

	if instance.Status.Status == dswv1.Running {
		changed, err := r.CredentialManager.IsCredentialKeyChanged(instance.ObjectMeta, ownerInstanceInfo)
		if err != nil {
			logger.Error(err, "failed to check if credential keys changed")
			if paicredential.IsRetriableError(err) {
				return ctrl.Result{}, err
			}

			r.EventRecorder.Event(instance, corev1.EventTypeWarning, "CheckCredentialKeys", fmt.Sprintf("failed to check if credential keys changed: %v. ", err))
			// Instance is still running, so we return err directly to trigger requeue, rather than update instance status to failed.
			return ctrl.Result{}, err
		}

		if changed {
			logger.Info("credential keys changed in running instance, so update instance status to updating.")
			return r.updateInstanceStatusToUpdatingWhenInstanceIsRunning(instance)
		} else {
			if err = r.CredentialManager.ReconcileCredentials(instance.ObjectMeta, ownerInstanceInfo); err != nil {
				logger.Error(err, "failed to reconcile PAI credential for running instance")
				if paicredential.IsRetriableError(err) {
					return ctrl.Result{}, err
				}

				r.EventRecorder.Event(instance, corev1.EventTypeWarning, "ReconcilePaiCredential", fmt.Sprintf("failed to reconcile PAI credential: %v", err))
				// Instance is still running, so we return err directly to trigger requeue, rather than update instance status to failed.
				return ctrl.Result{}, err
			}
		}
	} else if instance.Status.Status == dswv1.Updating {
		changed, err := r.CredentialManager.IsCredentialKeyChanged(instance.ObjectMeta, ownerInstanceInfo)
		if err != nil {
			logger.Error(err, "failed to check if credential keys changed")
			if paicredential.IsRetriableError(err) {
				return ctrl.Result{}, err
			}

			r.EventRecorder.Event(instance, corev1.EventTypeWarning, "CheckCredentialKeys", fmt.Sprintf("failed to check if credential keys changed: %v. ", err))
			return r.updateStatus(instance, &dswv1.DswInstanceStatus{
				Status:  dswv1.Failed,
				Message: fmt.Sprintf("failed to check if credential keys changed: %v. ", err),
			})
		}

		if changed {
			logger.Info("credential keys changed, so restart notebook and create/delete credentials.")
			if result, err := r.deleteNotebook(instance); utils.HasErrorOrRequeue(result, err) {
				return result, err
			}

			if err = r.CredentialManager.ReconcileCredentials(instance.ObjectMeta, ownerInstanceInfo); err != nil {
				logger.Error(err, "Failed to reconcile PAI credential for updating instance")
				if paicredential.IsRetriableError(err) {
					return ctrl.Result{}, err
				}

				r.EventRecorder.Event(instance, corev1.EventTypeWarning, "ReconcilePaiCredential", fmt.Sprintf("failed to reconcile PAI credential: %v", err))
				return r.updateStatus(instance, &dswv1.DswInstanceStatus{
					Status:  dswv1.Failed,
					Message: fmt.Sprintf("failed to check if credential keys changed: %v. ", err),
				})
			}

			// keep requeue until credential keys consistent with config.
			return utils.Requeue(utils.ReconcileInterval)
		} else {
			// check if credential ready
			if ready, err := r.CredentialManager.IsCredentialReady(instance.ObjectMeta, ownerInstanceInfo); err != nil {
				logger.Error(err, "failed to check if credentials ready.")
				if paicredential.IsRetriableError(err) {
					return ctrl.Result{}, err
				}

				r.EventRecorder.Event(instance, corev1.EventTypeWarning, "CheckCredentials", fmt.Sprintf("failed to check if credentials are ready: %v. ", err))
				return r.updateStatus(instance, &dswv1.DswInstanceStatus{
					Status:  dswv1.Failed,
					Message: fmt.Sprintf("failed to check if credentials are ready: %v. ", err),
				})
			} else if !ready {
				logger.Info("credentials are not ready, so requeue.")
				return ctrl.Result{Requeue: true}, nil
			}
			return ctrl.Result{}, nil
		}
	} else {
		err = r.CredentialManager.ReconcileCredentials(instance.ObjectMeta, ownerInstanceInfo)
		if err != nil {
			logger.Error(err, "failed to reconcile PAI credential")
			if paicredential.IsRetriableError(err) {
				return ctrl.Result{}, err
			}

			r.EventRecorder.Event(instance, corev1.EventTypeWarning, "ReconcilePaiCredential", fmt.Sprintf("failed to reconcile PAI credential: %v", err))
			if instance.Status.Status == dswv1.ResourceAllocating || instance.Status.Status == dswv1.EnvPreparing ||
				instance.Status.Status == dswv1.Queuing { // instance should be protected, if in other status

				return r.updateStatus(instance, &dswv1.DswInstanceStatus{
					Status:  dswv1.Failed,
					Message: fmt.Sprintf("failed to reconcile PAI credential: %v", err),
				})
			}
		}

		// check if credential ready
		if ready, err := r.CredentialManager.IsCredentialReady(instance.ObjectMeta, ownerInstanceInfo); err != nil {
			logger.Error(err, "failed to check if credentials ready.")
			if paicredential.IsRetriableError(err) {
				return ctrl.Result{}, err
			}

			r.EventRecorder.Event(instance, corev1.EventTypeWarning, "CheckCredentials", fmt.Sprintf("failed to check if credentials are ready: %v. ", err))
			if instance.Status.Status == dswv1.ResourceAllocating || instance.Status.Status == dswv1.EnvPreparing ||
				instance.Status.Status == dswv1.Queuing { // instance should be protected, if in other status

				return r.updateStatus(instance, &dswv1.DswInstanceStatus{
					Status:  dswv1.Failed,
					Message: fmt.Sprintf("failed to check if credentials are ready: %v. ", err),
				})
			}
		} else if !ready {
			logger.Info("credentials are not ready, so requeue.", "instance", instance)
			return utils.Requeue(utils.ReconcileInterval)
		}
	}

	return ctrl.Result{}, nil
}

func getWorkspaceId(instance *dswv1.DswInstance) (string, error) {
	if workspaceId, exist := instance.Labels[utils.LabelKeyWorkspaceId]; exist {
		return workspaceId, nil
	}

	return utils.EmptyString, fmt.Errorf("label workspace id not found. ")
}

func (r *DswInstanceReconciler) getNamespace(instance *dswv1.DswInstance) string {
	return utils.GetNamespace(instance, r.Config)
}

func (r *DswInstanceReconciler) reconcileNotebook(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	var foundNotebook = &dswv1.Notebook{}
	if err := r.Get(context.TODO(), types.NamespacedName{Name: instance.Name, Namespace: r.getNamespace(instance)}, foundNotebook); err != nil {
		if apierrs.IsNotFound(err) {
			if r.Config.EnableIdleInstanceCuller {
				if result, err := r.IdleInstanceCullerService.cleanup(instance); utils.HasErrorOrRequeue(result, err) {
					return result, err
				}
			}

			notebook, err := r.generateNotebook(instance)
			if err != nil {
				logger.Error(err, "Fail to generate Notebook")
				return ctrl.Result{}, err
			}

			//add ecsType to cache when spot instance init
			if r.Config.EnableSpotMetering && utils.IsSpot(instance) {
				resourceType, exists := notebook.Labels[utils.LabelKeyResourceType]
				if exists {
					r.SpotInstanceManager.AddSpotEcsTypeWithLock(resourceType)
				} else {
					logger.Info("Resource-type does not exist", "notebook", notebook.Name)
				}
			}

			logger.Info("Creating Notebook")
			if err := r.Create(context.TODO(), notebook); err != nil {
				logger.Error(err, "Create Notebook failed")
				return ctrl.Result{}, err
			} else {
				logger.Info("Create Notebook CR succeed")
			}
		} else {
			// Error during get Notebook
			return ctrl.Result{}, err
		}
	} else {
		logger = logger.WithValues("notebookName", foundNotebook.Name, "notebookNamespace", foundNotebook.Namespace)

		if result, err := r.reconcileNotebookForUpdating(foundNotebook, instance); utils.HasErrorOrRequeue(result, err) {
			return result, err
		}

		// Update notebook by patching
		if _, ok := utils.GetFromMap(foundNotebook.Annotations, utils.AnnotationVpcId); !ok {
			if instance.Spec.DLink != nil && instance.Spec.DLink.VpcID != "" {
				patch := client.MergeFrom(foundNotebook.DeepCopy())
				if foundNotebook.Annotations == nil {
					foundNotebook.Annotations = make(map[string]string)
				}
				foundNotebook.Annotations[utils.AnnotationVpcId] = instance.Spec.DLink.VpcID
				if err := r.Patch(context.TODO(), foundNotebook, patch); err != nil {
					logger.Error(err, "Failed to patch vpcId to notebook")
					return ctrl.Result{}, err
				}
			}
		}

		// Update DswInstance status according to Notebook status
		instanceImageTemplate := instance.Spec.ImageTemplate
		notebookImageTemplate := foundNotebook.Spec.ImageTemplate

		// notebookImageTemplate.DockerImageVersion == "" means no image saving job is running
		if (instanceImageTemplate.DockerImageVersion != "" && notebookImageTemplate.DockerImageVersion == "") ||
			(instanceImageTemplate.ContainerSnapshotName != "" && notebookImageTemplate.ContainerSnapshotName != instanceImageTemplate.ContainerSnapshotName) {

			patch := client.MergeFrom(foundNotebook.DeepCopy())
			foundNotebook.Spec.ImageTemplate = instanceImageTemplate
			err := r.Patch(context.TODO(), foundNotebook, patch)
			if err != nil {
				logger.Error(err, "Failed to patch image to notebook")
				return ctrl.Result{}, err
			}
			logger.Info("update notebook image template.",
				"repo", instanceImageTemplate.DockerRepository,
				"imageVersion", instanceImageTemplate.DockerImageVersion,
				"containerSnapshotName", instanceImageTemplate.ContainerSnapshotName)

			instancePatch := client.MergeFrom(instance.DeepCopy())
			instance.Spec.ImageTemplate = dswv1.ImageSpec{}
			err = r.Patch(context.TODO(), instance, instancePatch)
			if err != nil {
				logger.Error(err, "Failed to patch null image spec to instance")
				return ctrl.Result{}, err
			}
		}

		if instance.Status.Status == dswv1.Updating && foundNotebook.Status.Status == dswv1.Failed {
			if err := r.Delete(context.TODO(), foundNotebook); err != nil {
				if apierrs.IsNotFound(err) {
					return ctrl.Result{}, nil
				}
				logger.Error(err, "Fail to delete Notebook")
				return ctrl.Result{}, err
			}
			return utils.Requeue(utils.ReconcileInterval)
		}

		return r.updateStatus(instance, &dswv1.DswInstanceStatus{
			Status:      foundNotebook.Status.Status,
			Message:     foundNotebook.Status.Message,
			LastPodName: foundNotebook.Status.LastPodName,
		})
	}

	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) deleteDswCredential(instance *dswv1.DswInstance) (ctrl.Result, error) {
	if !instance.Spec.EnableMountCredential {
		return ctrl.Result{}, nil
	}

	namespace := r.getNamespace(instance)
	namespacedName := types.NamespacedName{
		Name:      instance.Name,
		Namespace: namespace,
	}

	foundCredential := &dswv1.Credential{}
	if err := r.Get(context.TODO(), namespacedName, foundCredential); err != nil {
		if !apierrs.IsNotFound(err) {
			return ctrl.Result{}, err
		}
		return ctrl.Result{}, nil
	}

	if err := r.Delete(context.TODO(), foundCredential); err != nil {
		logger := utils.GetLogger(r.Log, instance)
		logger.Error(err, "Fail to delete Credential")
		return ctrl.Result{}, err
	}
	return utils.Requeue(utils.ReconcileInterval)
}

func (r *DswInstanceReconciler) deletePaiCredential(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := r.Log.WithName("deletePaiCredential")

	if !r.Config.EnablePaiCredential {
		return ctrl.Result{}, nil
	}

	if !r.CredentialManager.EnableCredential(instance.ObjectMeta) {
		return ctrl.Result{}, nil
	}

	// list the credentials below specified instance by label selector
	ownerInstanceInfo, err := paicredential.BuildCredentialOwnerInstanceInfo(&instance.ObjectMeta, instance.Spec.UserId, utils.GetTenantNamespace(instance))
	if err != nil {
		logger.Error(err, "failed to build CredentialOwnerInstanceInfo.", "instance", instance)
		return ctrl.Result{}, fmt.Errorf("failed to build credential owner instance info: %v. ", err)
	}
	credentialList, err := r.CredentialManager.ListCredentials(ownerInstanceInfo)
	if err != nil {
		logger.Error(err, "failed to list pai credentials.", "OwnerInstanceInfo", ownerInstanceInfo)
		return ctrl.Result{}, fmt.Errorf("failed to list pai credentials: %v. ", err)
	}

	if len(credentialList.Items) == 0 {
		return ctrl.Result{}, nil
	}

	if err = r.CredentialManager.DeleteCredentials(credentialList); err != nil {
		logger.Error(err, "failed to delete pai credentials.", "CredentialList", credentialList)
		return ctrl.Result{}, fmt.Errorf("failed to delete pai credentials: %v. ", err)
	}

	return utils.Requeue(utils.ReconcileInterval)
}

func (r *DswInstanceReconciler) deleteNotebook(instance *dswv1.DswInstance) (ctrl.Result, error) {
	namespace := r.getNamespace(instance)
	namespacedName := types.NamespacedName{
		Name:      instance.Name,
		Namespace: namespace,
	}

	foundNotebook := &dswv1.Notebook{}
	if err := r.Get(context.TODO(), namespacedName, foundNotebook); err != nil {
		if !apierrs.IsNotFound(err) {
			return ctrl.Result{}, err
		}
		return ctrl.Result{}, nil
	}

	logger := utils.GetLogger(r.Log, instance).WithValues("namespace", namespace)
	if err := r.Delete(context.TODO(), foundNotebook); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		}

		logger.Error(err, "Fail to delete Notebook")
		return ctrl.Result{}, err
	}

	logger.Info("Wait until Notebook is deleted")
	return utils.Requeue(utils.ReconcileInterval)
}

func (r *DswInstanceReconciler) updateStatus(instance *dswv1.DswInstance, status *dswv1.DswInstanceStatus) (ctrl.Result, error) {
	err := k8sretry.RetryOnConflict(k8sretry.DefaultBackoff, func() error {
		logger := utils.GetLogger(r.Log, instance)
		foundInstance := &dswv1.DswInstance{}
		if err := r.Get(context.TODO(), types.NamespacedName{Name: instance.Name, Namespace: instance.Namespace}, foundInstance); err != nil {
			if apierrs.IsNotFound(err) {
				return nil
			}
			logger.Error(err, "Get dsw instance error")
			return err
		}

		statusUpdated := false
		if status.Status != "" {
			if foundInstance.Status.Status != status.Status {
				r.EventRecorder.Event(instance, corev1.EventTypeNormal, "UpdateStatus",
					fmt.Sprintf("Status updated from %s to %s", foundInstance.Status.Status, status.Status))
				foundInstance.Status.Status = status.Status
				foundInstance.Status.StatusLastUpdateTime = &metav1.Time{Time: time.Now()}

				if status.Status != dswv1.Failed && status.Status != dswv1.Recovering {
					// Should update message at the same time, otherwise the message of previous status will reserve
					// if the status is fail and the message is blank, reserve the status message
					foundInstance.Status.Message = status.Message
				}

				statusUpdated = true
			}
		}

		if status.Message != "" {
			if foundInstance.Status.Message != status.Message {
				foundInstance.Status.Message = status.Message
				statusUpdated = true
				logger.Info(fmt.Sprintf("Message updated from %s to %s", foundInstance.Status.Message, status.Message))
			}
		}

		if status.EcsId != "" {
			if foundInstance.Status.EcsId != status.EcsId {
				foundInstance.Status.EcsId = status.EcsId
				statusUpdated = true
			}
		}

		if status.NodeName != "" {
			if foundInstance.Status.NodeName != status.NodeName {
				foundInstance.Status.NodeName = status.NodeName
				statusUpdated = true
			}
		}

		if status.SpecUpdateTime != nil {
			if foundInstance.Status.SpecUpdateTime != status.SpecUpdateTime {
				foundInstance.Status.SpecUpdateTime = status.SpecUpdateTime
				statusUpdated = true
			}
		}

		if status.TenantNamespace != "" {
			if foundInstance.Status.TenantNamespace != status.TenantNamespace {
				foundInstance.Status.TenantNamespace = status.TenantNamespace
				statusUpdated = true
			}
		}

		if status.ResourceGroup != "" {
			if foundInstance.Status.ResourceGroup != status.ResourceGroup {
				foundInstance.Status.ResourceGroup = status.ResourceGroup
				statusUpdated = true
			}
		}

		if status.LastPodName != "" {
			if foundInstance.Status.LastPodName != status.LastPodName {
				foundInstance.Status.LastPodName = status.LastPodName
				statusUpdated = true
			}
		}

		if statusUpdated {
			now := metav1.Now()
			foundInstance.Status.LastUpdateTime = &now
			err := r.Status().Update(context.TODO(), foundInstance)
			if err != nil {
				if !apierrs.IsConflict(err) {
					logger.Error(err, "update dsw instance status failed", "status", status)
				}
			} else {
				logger.Info("update dsw instance status succeed", "status", status)
			}
			return err
		}

		return nil
	})

	return ctrl.Result{}, err
}

func (r *DswInstanceReconciler) reconcileKoordinatorGpuResource(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	if !r.Config.WithAsiLingjun() {
		return ctrl.Result{}, nil
	}

	gpuShareEnabled := false
	for idx := range instance.Spec.Template.Spec.Containers {
		container := &instance.Spec.Template.Spec.Containers[idx]
		if container.Name != utils.DswNotebookContainerName {
			continue
		}

		var gpuRatio *resource.Quantity
		if gpuMemoryRatio, ok := container.Resources.Requests[common.AlibabaCloudGpuMemoryRatio]; ok {
			gpuRatio = &gpuMemoryRatio
		} else if gpuMemoryRatio, ok := container.Resources.Requests[common.KoordinatorGpuMemoryRatio]; ok {
			gpuRatio = &gpuMemoryRatio
		}

		if gpuRatio != nil {
			gpuShareEnabled = true
			// Set gpu memory and core ratios
			container.Resources.Requests[common.KoordinatorGpuMemoryRatio] = *gpuRatio
			container.Resources.Limits[common.KoordinatorGpuMemoryRatio] = *gpuRatio
			container.Resources.Requests[common.KoordinatorGpuCore] = *gpuRatio
			container.Resources.Limits[common.KoordinatorGpuCore] = *gpuRatio

			// Enable gpu share
			gpuShareQuantity, _ := resource.ParseQuantity("1")
			container.Resources.Requests[common.KoordinatorGpuShared] = gpuShareQuantity
			container.Resources.Limits[common.KoordinatorGpuShared] = gpuShareQuantity

			// Remove legacy GPU memory ratio
			delete(container.Resources.Requests, common.AlibabaCloudGpuMemoryRatio)
			delete(container.Resources.Limits, common.AlibabaCloudGpuMemoryRatio)

			// Adjust memory for KuberGPU overhead
			r.adjustMemoryForKuberGPUOverhead(instance, container)
		}
		break
	}

	if !gpuShareEnabled {
		return ctrl.Result{}, nil
	}

	// Initialize annotations and labels
	if instance.Annotations == nil {
		instance.Annotations = make(map[string]string)
	}
	if instance.Labels == nil {
		instance.Labels = make(map[string]string)
	}

	instance.Annotations[common.AnnotationRunDOverrideRuntimeHandler] = common.AnnotationRunDRundLatest

	if instance.Annotations[common.AnnotationSupportKuberGPU] == stringUtil.TrueString {
		if err := r.configureKuberGPUFeatures(instance); err != nil {
			return ctrl.Result{}, err
		}
	} else {
		if err := r.configureLegacySGPUFeatures(instance); err != nil {
			return ctrl.Result{}, err
		}
	}

	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) configureLegacySGPUFeatures(instance *dswv1.DswInstance) error {
	// Configure legacy SGPU features
	features := make(map[string]interface{})
	if existingFeatures, exists := instance.Annotations[common.RundFeatureAnnotationKey]; exists {
		if err := json.Unmarshal([]byte(existingFeatures), &features); err != nil {
			return err
		}
	}
	features[rundFeatureSgpu] = make(map[string]interface{})

	updatedFeatures, err := json.Marshal(features)
	if err != nil {
		return err
	}
	instance.Annotations[common.RundFeatureAnnotationKey] = string(updatedFeatures)

	if err := rdma.SetSgpuRdmaDeviceAllocateHintsAnnotation(instance); err != nil {
		return err
	}
	if err := rdma.SetSgpuRdmaDeviceJointAllocateAnnotation(instance); err != nil {
		return err
	}

	// Remove NvidiaGPUDriver when gpu shared is enabled
	delete(instance.Annotations, utils.AnnotationNvidiaGPUDriver)

	return nil
}

// configureKuberGPUFeatures configures KuberGPU-specific features and annotations
func (r *DswInstanceReconciler) configureKuberGPUFeatures(instance *dswv1.DswInstance) error {
	// Configure KuberGPU features
	features := make(map[string]interface{})
	if existingFeatures, exists := instance.Annotations[common.RundFeatureAnnotationKey]; exists {
		if err := json.Unmarshal([]byte(existingFeatures), &features); err != nil {
			return err
		}
	}
	features[common.RundFeatureSrvPodKuberGPU] = make(map[string]interface{})

	updatedFeatures, err := json.Marshal(features)
	if err != nil {
		return err
	}
	instance.Annotations[common.RundFeatureAnnotationKey] = string(updatedFeatures)

	// Set KuberGPU labels
	instance.Labels[common.LabelKeyKoordinatorUseSrvPodMode] = stringUtil.TrueString
	instance.Labels[common.LabelKeyGPUSharePolicy] = common.LabelValueGPUSharePolicyStatic
	instance.Labels[common.LabelHardwareGpuType] = stringUtil.TrueString

	// Configure RDMA settings
	if err := rdma.SetKuberGPURdmaDeviceAllocateHintsAnnotation(instance); err != nil {
		return err
	}
	if err := rdma.SetKuberGPURdmaDeviceJointAllocateAnnotation(instance); err != nil {
		return err
	}

	return nil
}

// adjustMemoryForKuberGPUOverhead adjusts container memory limits to account for KuberGPU overhead
func (r *DswInstanceReconciler) adjustMemoryForKuberGPUOverhead(instance *dswv1.DswInstance, container *corev1.Container) {
	if instance.Annotations != nil && instance.Annotations[common.AnnotationSupportKuberGPU] == stringUtil.TrueString {
		kubergpuMemoryOverhead := resource.MustParse(common.KubergpuMemoryOverhead)
		minMemoryFloor := resource.MustParse(common.RundMinMemory)

		for _, resourceMap := range []corev1.ResourceList{container.Resources.Requests, container.Resources.Limits} {
			if currentMemory, ok := resourceMap[corev1.ResourceMemory]; ok {
				adjustedMemory := currentMemory.DeepCopy()
				adjustedMemory.Sub(kubergpuMemoryOverhead)
				if adjustedMemory.Cmp(minMemoryFloor) >= 0 {
					// Create a canonical representation to ensure stable comparisons
					// Parse the string representation to normalize the internal format
					canonicalMemory := resource.MustParse(adjustedMemory.String())
					resourceMap[corev1.ResourceMemory] = canonicalMemory
				}
			}
		}
	}
}

// reconcile the datasource list if provided
func (r *DswInstanceReconciler) reconcileDataSources(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	// create the status to be mutated when it needs to be updated
	instanceStatus := &dswv1.DswInstanceStatus{}
	logger := utils.GetLogger(r.Log, instance)
	// reconcile the datasource list by datasource service
	result, err := r.DataSourceService.reconcile(ctx, instance, instanceStatus, r)
	if err != nil {
		// Retry if internal error occurs
		logger.Error(err, "Failed to reconcile datasources for dswinstance")
		if dlcUtils.IsInternalError(err) {
			return ctrl.Result{}, err
		}
	}
	// update the status if it is not empty
	if utils.IsNotBlank(instanceStatus.Status) || utils.IsNotBlank(instanceStatus.Message) {
		_, err = r.updateStatus(instance, instanceStatus)
		if err != nil {
			return ctrl.Result{}, err
		}
	}
	return result, err
}

func (r *DswInstanceReconciler) getInstanceInfoMapFromAnnotation(instance *dswv1.DswInstance) map[string]string {
	result := make(map[string]string)
	for k, v := range instance.Annotations {
		if strings.HasPrefix(k, utils.AnnotationInstanceInfoPrefix) {
			configKey := strings.TrimPrefix(k, utils.AnnotationInstanceInfoPrefix)
			if len(configKey) > 0 {
				result[configKey] = v
			}
		}
	}
	return result
}

func (r *DswInstanceReconciler) reconcileInstanceInfoConfigMap(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	instanceInfoMap := r.getInstanceInfoMapFromAnnotation(instance)
	if len(instanceInfoMap) == 0 {
		return ctrl.Result{}, nil
	}
	configMapName := utils.GetInstanceExtraInfoConfigMapName(instance.Name)
	namespace := utils.GetTenantNamespace(instance)

	found := &corev1.ConfigMap{}
	err := r.Get(context.TODO(), types.NamespacedName{Name: configMapName, Namespace: namespace}, found)
	if err != nil {
		if apierrs.IsNotFound(err) {
			configMap := &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:      configMapName,
					Namespace: namespace,
				},
				Data: instanceInfoMap,
			}
			if err = r.Create(context.TODO(), configMap); err != nil {
				if !apierrs.IsAlreadyExists(err) {
					r.EventRecorder.Event(
						instance, corev1.EventTypeWarning, "CreateConfigMap",
						fmt.Sprintf("Fail to create configMap %s/%s", configMap.Namespace, configMap.Name))
				}
				return ctrl.Result{}, err
			}
			r.EventRecorder.Event(
				instance, corev1.EventTypeNormal, "CreateConfigMap",
				fmt.Sprintf("Created configMap %s/%s", configMap.Namespace, configMap.Name))
		} else {
			return ctrl.Result{}, err
		}
	} else {
		if !cmp.Equal(found.Data, instanceInfoMap) {
			found.Data = instanceInfoMap
			if err = r.Update(context.TODO(), found); err != nil {
				if !apierrs.IsConflict(err) {
					r.EventRecorder.Event(
						instance, corev1.EventTypeWarning, "UpdateConfigMap",
						fmt.Sprintf("Fail to create configMap %s/%s", found.Namespace, found.Name))
				}
				return ctrl.Result{}, err
			}
			r.EventRecorder.Event(
				instance, corev1.EventTypeNormal, "UpdateConfigMap",
				fmt.Sprintf("Updated configMap %s/%s", found.Namespace, found.Name))
		}
	}

	container := utils.GetDswNotebookContainer(instance)
	if container != nil {
		// contains configmap
		container.VolumeMounts = utils.AppendVolumeMountIfNotExist(
			container.VolumeMounts, corev1.VolumeMount{
				Name:      utils.VolumeNameDswInstanceInfo,
				MountPath: utils.VolumeMountPathDswInstance,
				ReadOnly:  false,
			},
		)
	}
	var items []corev1.KeyToPath
	for k := range instanceInfoMap {
		items = append(items, corev1.KeyToPath{
			Key:  k,
			Path: k,
		})
	}
	volumes := instance.Spec.Template.Spec.Volumes
	instance.Spec.Template.Spec.Volumes = utils.AppendVolumeIfNotExist(
		volumes,
		corev1.Volume{
			Name: utils.VolumeNameDswInstanceInfo,
			VolumeSource: corev1.VolumeSource{
				ConfigMap: &corev1.ConfigMapVolumeSource{
					LocalObjectReference: corev1.LocalObjectReference{Name: configMapName},
					Items:                items,
				},
			}})
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) deleteDswInstanceInfoConfigMap(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	found := &corev1.ConfigMap{}
	configMapName := utils.GetInstanceExtraInfoConfigMapName(instance.Name)
	namespace := utils.GetTenantNamespace(instance)
	if err := r.Get(context.TODO(), types.NamespacedName{Name: configMapName, Namespace: namespace}, found); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		} else {
			return ctrl.Result{}, err
		}
	}
	if err := r.Delete(context.TODO(), found); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		}
		logger.Error(err, "Delete instance extra info config map failed", "name", found.Name, "namespace", found.Namespace)
		return ctrl.Result{}, err
	} else {
		logger.Error(err, "Delete instance extra info config map succeed", "name", found.Name, "namespace", found.Namespace)
	}
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) deleteTerminatingPod(instance *dswv1.DswInstance) (ctrl.Result, error) {
	var pods = &corev1.PodList{}
	namespace := r.getNamespace(instance)
	if err := r.List(context.TODO(), pods, client.InNamespace(namespace), client.MatchingLabels{
		utils.LabelKeyDeployment: instance.Name}); err != nil {
		return ctrl.Result{}, err
	}

	logger := utils.GetLogger(r.Log, instance)
	for _, pod := range pods.Items {
		// Filter out Terminating Pods
		if pod.DeletionTimestamp == nil {
			return utils.Requeue(utils.ReconcileInterval)
		}

		// Wait controller to delete pod normally within grace period
		if !isTimeOut(pod.DeletionTimestamp, *pod.DeletionGracePeriodSeconds) {
			return utils.Requeue(utils.ReconcileInterval)
		}

		// If it is post paid instance we need to force delete pod
		if instance.Spec.ResourceGroupID == "" {
			logger.Info("Force delete pod after DeletionGracePeriod", "name", pod.Name, "namespace", pod.Namespace)
			gracePeriodSeconds := int64(0)
			return ctrl.Result{}, r.Delete(context.TODO(), &pod, &client.DeleteOptions{
				GracePeriodSeconds: &gracePeriodSeconds,
			})
		}

		logger.Info("[ALERT] detect Terminating pod after DeletionGracePeriod", "name", pod.Name, "namespace", pod.Namespace)
		// TODO:  the surrounding loop is unconditionally terminated
		return ctrl.Result{}, nil // nolint
	}

	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) reconcileSSHGateway(instance *dswv1.DswInstance) (ctrl.Result, error) {
	if instance.GetAnnotations() == nil {
		return ctrl.Result{}, nil
	}

	port, _ := instance.GetAnnotations()[utils.AnnotationSSHGatewayPort]
	if port == "" {
		return r.deleteSSHGatewayIfExists(instance)
	}

	logger := utils.GetLogger(r.Log, instance)
	namespace := r.getNamespace(instance)
	name := instance.Name
	portInt, _ := strconv.Atoi(port)

	gateway := gatewayv1.Gateway{
		ObjectMeta: metav1.ObjectMeta{
			Name:      instance.Name,
			Namespace: r.Config.GatewayNamespace,
			Labels: map[string]string{
				utils.AnnotationSSHGatewayPort: instance.GetAnnotations()[utils.AnnotationSSHGatewayPort],
			},
		},
		Spec: gatewayapi.Gateway{
			UseProxyProto: wrapperspb.Bool(false),
			BindAddress:   "::",
			BindPort:      uint32(portInt),
			GatewayType: &gatewayapi.Gateway_TcpGateway{
				TcpGateway: &gatewayapi.TcpGateway{
					TcpHosts: []*gatewayapiv1.TcpHost{
						{
							Name: "ssh",
							Destination: &gatewayapiv1.TcpHost_TcpAction{
								Destination: &gatewayapiv1.TcpHost_TcpAction_Single{
									Single: &gatewayapiv1.Destination{
										DestinationType: &gatewayapiv1.Destination_Upstream{
											Upstream: &gatewaycore.ResourceRef{
												Name:      fmt.Sprintf("%s-%s-22", namespace, name),
												Namespace: r.Config.GatewayNamespace,
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	existGateway := &gatewayv1.Gateway{}
	if err := r.Client.Get(context.TODO(), types.NamespacedName{
		Namespace: r.Config.GatewayNamespace,
		Name:      name,
	}, existGateway); err != nil {
		if !apierrs.IsNotFound(err) {
			return ctrl.Result{}, err
		}
	} else {
		if existGateway.Spec.BindPort == uint32(portInt) {
			// Gateway already exist
			return ctrl.Result{}, nil
		} else {
			// delete and recreate a new one
			logger.Info("Delete gateway CR because of different port", "namespace", r.Config.GatewayNamespace, "name", name, "port", existGateway.Spec.BindPort, "expected", port)
			if err := r.Client.Delete(context.TODO(), existGateway); err != nil {
				return ctrl.Result{}, err
			}
		}
	}

	// Create the gateway CR
	logger.Info("Create gateway CR", "namespace", r.Config.GatewayNamespace)
	return ctrl.Result{}, r.Client.Create(context.TODO(), &gateway)
}

func (r *DswInstanceReconciler) deleteSSHGatewayIfExists(instance *dswv1.DswInstance) (ctrl.Result, error) {
	gateway := gatewayv1.Gateway{}
	if err := r.Client.Get(context.TODO(), types.NamespacedName{
		Namespace: r.Config.GatewayNamespace,
		Name:      instance.Name,
	}, &gateway); err != nil {
		if apierrs.IsNotFound(err) {
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}

	logger := utils.GetLogger(r.Log, instance)
	// delete the gateway CR
	logger.Info("Delete gateway CR", "namespace", r.Config.GatewayNamespace)
	return ctrl.Result{}, r.Client.Delete(context.TODO(), &gateway)
}

func (r *DswInstanceReconciler) reconcileMixedQueue(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	annotation := instance.Annotations
	val, ok := annotation[utils.XingyunInQueueKey]
	if !ok {
		if instance.Annotations == nil {
			instance.Annotations = map[string]string{}
		}
		instancePatch := client.MergeFrom(instance.DeepCopy())
		instance.Annotations[utils.XingyunInQueueKey] = utils.XingyunQueueInitValue
		instance.Annotations[utils.XingyunJobReIdKey] = uuid.New().String()
		err := r.Patch(context.TODO(), instance, instancePatch)
		if err != nil {
			logger.Error(err, "Failed to patch mixed queue annotation to instance")
			return ctrl.Result{}, err
		}
		_, err = r.updateStatus(instance, &dswv1.DswInstanceStatus{
			Status:         dswv1.Starting,
			SpecUpdateTime: &metav1.Time{Time: time.Now()},
		})
		if err != nil {
			return ctrl.Result{}, err
		}
		return utils.Requeue(utils.ReconcileInterval)
	}
	if val == utils.XingyunDealedQueuingValue {
		queueStatus, ok := annotation[utils.XingyunQueueStatus]
		if ok {
			if queueStatus == utils.XingyunQueueDequeueValue {
				// if dequeued, continue the next procession
				return ctrl.Result{}, nil
			} else if queueStatus == utils.XingyunQueueQueuingValue {
				// if queuing, reconcile the instance again
				return utils.Requeue(utils.ReconcileInterval)
			} else if queueStatus == utils.XingyunQueueErrorValue {
				// if err, set the instance status as failed
				errMsg := "Fail to queue in mixed queue"
				if msgVal, ok := annotation[utils.XingyunQueueErrMessageKey]; ok {
					errMsg = msgVal
				}
				if result, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
					Status:  dswv1.Failed,
					Message: errMsg,
				}); err != nil {
					return result, err
				}
				return utils.Requeue(utils.ReconcileShortInterval)
			} else {
				// the queue status is considered as unknown, so we think it fails to queue
				if result, err := r.updateStatus(instance, &dswv1.DswInstanceStatus{
					Status:  dswv1.Failed,
					Message: "Unknown status in mixed queue",
				}); err != nil {
					return result, err
				}
				return utils.Requeue(utils.ReconcileShortInterval)
			}
		}
	}
	return utils.Requeue(utils.ReconcileInterval)
}

// remove annotation for the mixed queue, and the next reconcile will generate a new annotation
func (r *DswInstanceReconciler) requeueInMixedQueue(instance *dswv1.DswInstance) error {
	logger := utils.GetLogger(r.Log, instance)
	instancePatch := client.MergeFrom(instance.DeepCopy())
	delete(instance.Annotations, utils.XingyunInQueueKey)
	delete(instance.Annotations, utils.XingyunQueueStatus)
	err := r.Patch(context.TODO(), instance, instancePatch)
	if err != nil {
		logger.Error(err, "Failed to patch mixed queue annotation to instance")
		return err
	}
	logger.Info("Mixed queue annotation has been removed")
	time.Sleep(time.Duration(10000))
	return nil
}

func (r *DswInstanceReconciler) reconcileCloudDiskImageSHA256(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	return r.DataSourceService.reconcileCloudDiskImageSHA256(instance)
}

func (r *DswInstanceReconciler) reconcileDataSourceForBilling(ctx context.Context, instance *dswv1.DswInstance) (ctrl.Result, error) {
	return r.DataSourceService.reconcileDataSourceForBilling(instance)
}

func (r *DswInstanceReconciler) reconcileLingjunSpotJobBids(instance *dswv1.DswInstance) (ctrl.Result, error) {
	logger := utils.GetLogger(r.Log, instance)
	if !utils.IsLingjunSpot(instance) || !featureUtil.DefaultFeatureGate.Enabled(features.EnableLingjunSpotScheme) {
		return ctrl.Result{}, nil
	}
	logger.Info("Begin to reconcileLingjunSpotJobBids", "instance", instance.Name)
	if instance.Annotations[spot.AnnotationSpotDiscountLimit] == "" {
		err := fmt.Errorf("AnnotationSpotDiscountLimit is null")
		logger.Error(err, "AnnotationSpotDiscountLimit is null")
		return ctrl.Result{}, err
	}
	namespace := r.getNamespace(instance)
	discountLimit := instance.Annotations[spot.AnnotationSpotDiscountLimit]
	bid := &spotv1.SpotJobBid{}
	err := r.APIReader.Get(context.Background(), types.NamespacedName{
		Name:      instance.Name,
		Namespace: namespace,
	}, bid)
	if err == nil {
		return ctrl.Result{}, nil
	}
	if !apierrs.IsNotFound(err) {
		logger.Info("Failed to get SpotJobBid", "error", err)
		return ctrl.Result{}, err
	}
	newBid := &spotv1.SpotJobBid{
		ObjectMeta: metav1.ObjectMeta{
			Name:      instance.Name,
			Namespace: namespace,
			Labels: map[string]string{
				utils.LabelKeyMetricTenantId: instance.Spec.UserId,
			},
		},
		Spec: spotv1.SpotJobBidSpec{Kind: dswv1.DswInstanceKind},
	}
	spotInstanceTypeName := instance.Spec.InstanceType
	spotInstanceType := &spotv1.SpotInstanceType{}
	if err = r.APIReader.Get(
		context.Background(),
		types.NamespacedName{Name: spotInstanceTypeName}, spotInstanceType); err != nil {
		logger.Info("Failed to get SpotInstanceType", "error", err)
		return ctrl.Result{}, err
	}
	subBid := &spotv1.SubBid{
		Instance: spotInstanceType.Spec,
		MaxPrice: discountLimit,
		Replica:  1,
	}
	newBid.Spec.SubBids = append(newBid.Spec.SubBids, *subBid)
	if err = r.Create(context.Background(), newBid); err != nil {
		logger.Info("Failed to create SpotJobBid", "error", err)
		return ctrl.Result{}, err
	}
	return ctrl.Result{}, nil
}

func (r *DswInstanceReconciler) deleteLingjunSpotJobBids(instance *dswv1.DswInstance) (ctrl.Result, error) {
	if !featureUtil.DefaultFeatureGate.Enabled(features.EnableLingjunSpotScheme) {
		return ctrl.Result{}, nil
	}
	namespace := r.getNamespace(instance)
	namespacedName := types.NamespacedName{
		Name:      instance.Name,
		Namespace: namespace,
	}

	bid := &spotv1.SpotJobBid{}
	if err := r.Get(context.TODO(), namespacedName, bid); err != nil {
		if !apierrs.IsNotFound(err) {
			return ctrl.Result{}, err
		}
		return ctrl.Result{}, nil
	}

	if err := r.Delete(context.TODO(), bid); err != nil {
		logger := utils.GetLogger(r.Log, instance)
		logger.Error(err, "Fail to delete Lingjun SpotJobBids")
		return ctrl.Result{}, err
	}
	return utils.Requeue(utils.ReconcileInterval)
}

func (r *DswInstanceReconciler) spotSpecChanged(resourceGroup *rgv1.ResourceGroup, instance *dswv1.DswInstance) bool {
	spotSpec := resourceGroup.Spec.Resources[instance.Name].SpotSpec
	ecsSpotStrategy := instance.Annotations[spot.AnnotationEcsSpotStrategy]
	ecsSpotPriceLimit := instance.Annotations[spot.AnnotationEcsSpotPriceLimit]
	ecsSpotDuration := instance.Annotations[spot.AnnotationEcsSpotDuration]
	lingjunSpotStrategy := instance.Annotations[spot.AnnotationLingjunSpotStrategy]
	spotDiscountLimit := instance.Annotations[spot.AnnotationSpotDiscountLimit]
	if spotSpec != nil {
		strategy := spotSpec.Strategy
		provider := spotSpec.SpotProvider
		discountLimit := spotSpec.DiscountLimit
		duration := spotSpec.Duration
		pricelimit := spotSpec.PriceLimit
		if ecsSpotStrategy == "" && lingjunSpotStrategy == "" {
			r.Log.Info("ecsSpotStrategy and lingjunSpotStrategy is null", "instance", instance.Name)
			return true
		} else if ecsSpotStrategy != "" {
			if provider != rgv1.SpotProviderECS {
				r.Log.Info("spot provider is changed to ecs", "instance", instance.Name)
				return true
			}
			if ecsSpotStrategy != string(strategy) {
				r.Log.Info("ecs spot strategy is changed ", "instance", instance.Name)
				return true
			}
			if duration != ecsSpotDuration {
				r.Log.Info("ecs spot duration is changed ", "instance", instance.Name)
				return true
			}
			priceHourLimit, parseErr := strconv.ParseFloat(ecsSpotPriceLimit, 64)
			if parseErr != nil {
				r.Log.Error(parseErr, "ecs spot price limit format error")
				return false
			}
			tempPriceMinute := priceHourLimit / 60
			strValue := strconv.FormatFloat(tempPriceMinute, 'f', 6, 64)
			priceMinuteLimit, quantityErr := resource.ParseQuantity(strValue)
			if quantityErr != nil {
				r.Log.Error(quantityErr, "ecs spot price limit quantity error")
				return false
			}
			if pricelimit != priceMinuteLimit {
				r.Log.Info("ecs spot price is changed ", "instance", instance.Name)
				return true
			}
		} else if lingjunSpotStrategy != "" {
			if provider != rgv1.SpotProviderLingjun {
				r.Log.Info("spot provider is changed to lingjun", "instance", instance.Name)
				return true
			}
			if spotDiscountLimit != "" {
				lingjunDiscountLimit, quantityErr := resource.ParseQuantity(spotDiscountLimit)
				if quantityErr != nil {
					r.Log.Error(quantityErr, "lingjun spot discount limit format error")
					return false
				}
				if !lingjunDiscountLimit.Equal(discountLimit) {
					r.Log.Info("lingjun spot discount is changed ", "instance", instance.Name)
					return true
				}
			}
		}
	} else if ecsSpotStrategy != "" || lingjunSpotStrategy != "" {
		r.Log.Info("instance is changed to spot", "instance", instance.Name)
		return true
	}
	return false
}
