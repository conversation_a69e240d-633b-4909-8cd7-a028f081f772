package controllers

import (
	"context"
	"fmt"
	dswv1 "pai-mono/apis/dsw-controllers/v1"
	stsutils "pai-mono/pkg/dsw-controllers/aliyun/sts"
	"pai-mono/pkg/dsw-controllers/configs"
	"pai-mono/pkg/dsw-controllers/utils"
	"pai-mono/pkg/pai-dlc-operator/common/aliyun/sts"
	"pai-mono/pkg/util/grayrelease"
	"time"

	"github.com/go-logr/logr"
	"github.com/petermattis/goid"
	stssdk "gitlab.alibaba-inc.com/ram-sdk-family/sts-openapi-go-sdk/sdk/sts"
	corev1 "k8s.io/api/core/v1"
	apierrs "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	k8sretry "k8s.io/client-go/util/retry"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

const (
	defaultStsDurationSeconds                    = 3600
	defaultCredentialBackgroundReconcileInterval = 60
	defaultCredentialReconcileInterval           = 1
	defaultStsDurationAheadMinutes               = 10
	expirationTimeLayout                         = "2006-01-02T15:04:05Z"
)

// CredentialReconciler reconciles a Credential object
type CredentialReconciler struct {
	client.Client
	Log           logr.Logger
	Scheme        *runtime.Scheme
	EventRecorder record.EventRecorder
	Config        *configs.Config
	StsService    *stsutils.Service
}

// Reconcile is the entrypoint of credential controller
// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=credentials,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=dsw.alibaba.com,resources=credentials/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=core,resources=secrets,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=secrets/status,verbs=get;update;patch
func (r *CredentialReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := r.Log.WithValues("credential", req.NamespacedName, "goId", goid.Get())
	credential := &dswv1.Credential{}

	err := r.Get(ctx, req.NamespacedName, credential)
	if err != nil {
		if apierrs.IsNotFound(err) {
			logger.Info("Not found Credential")
			return ctrl.Result{}, nil
		}
		return ctrl.Result{}, err
	}

	logger = utils.GetLogger(logger, credential)

	if credential.Status.Status == "" {
		if _, err := r.updateStatus(credential, &dswv1.CredentialStatus{
			Status: dswv1.CredentialStatusInitialized,
		}); err != nil {
			return ctrl.Result{}, err
		}
	}

	// Create secret if not exist
	var foundSecret = &corev1.Secret{}
	if err := r.Get(context.TODO(), types.NamespacedName{Name: credential.Name, Namespace: credential.Namespace}, foundSecret); err != nil && apierrs.IsNotFound(err) {
		secret := &corev1.Secret{
			ObjectMeta: metav1.ObjectMeta{
				Name:      credential.Name,
				Namespace: credential.Namespace,
			},
		}

		// Set owner reference
		if err := controllerutil.SetControllerReference(credential, secret, r.Scheme); err != nil {
			return ctrl.Result{}, err
		}

		err = r.Create(context.TODO(), secret)
		if err != nil {
			return ctrl.Result{}, err
		}

		return utils.Requeue(defaultCredentialReconcileInterval)
	}

	if credential.Status.Credential != nil {
		expirationTime, err := time.Parse(expirationTimeLayout, credential.Status.Credential.Expiration)
		if err != nil {
			return ctrl.Result{}, err
		}

		if time.Now().Before(expirationTime.Add(time.Duration(-defaultStsDurationAheadMinutes) * time.Minute)) {
			return utils.Requeue(defaultCredentialBackgroundReconcileInterval)
		}
	}

	credentialSpec := credential.Spec
	stsConfig := credentialSpec.StsConfig
	roleArn := credentialSpec.RoleArn
	if utils.IsBlank(roleArn) && stsConfig == nil {
		noRoleSuppliedErr := fmt.Errorf("no role arn or role chain is supplied for %s", req.NamespacedName)
		return ctrl.Result{}, noRoleSuppliedErr
	}
	if utils.IsNotBlank(roleArn) && stsConfig != nil {
		bothRoleSuppliedErr := fmt.Errorf("both role arn and role chain are supplied for %s", req.NamespacedName)
		return ctrl.Result{}, bothRoleSuppliedErr
	}

	var credentialInfo *dswv1.CredentialInfo
	// handle with stsConfig
	if stsConfig != nil {
		logger.Info("create credential by sts config")
		credentialInfo, err = r.StsService.AssumeRoleListWithServiceIdentityAndStsAccessKey(r.Config.ServiceAccountAccessKeyId, r.Config.ServiceAccountAccessKeySecret, stsConfig, defaultStsDurationSeconds)
		if err != nil {
			logger.Error(err, "AssumeRoleListWithServiceIdentityAndStsAccessKey failed")
			if _, err := r.updateStatus(credential, &dswv1.CredentialStatus{
				Status:       dswv1.CredentialStatusFailed,
				ErrorMessage: err.Error(),
			}); err != nil {
				return ctrl.Result{}, err
			}
			return ctrl.Result{}, err
		}
	}
	// handle with role arn
	if utils.IsNotBlank(roleArn) {
		logger.Info("create credential by role arn")
		stsManager, err := sts.NewStsManager(
			r.Config.Region,
			r.Config.EndpointNetwork,
			r.Config.EndpointType,
			r.Config.ServiceAccountAccessKeyId,
			r.Config.ServiceAccountAccessKeySecret,
			r.Config.StsEndpoint)

		if err != nil {
			if _, err := r.updateStatus(credential, &dswv1.CredentialStatus{
				Status:       dswv1.CredentialStatusFailed,
				ErrorMessage: err.Error(),
			}); err != nil {
				return ctrl.Result{}, err
			}
			return ctrl.Result{}, err
		}

		// Update secret
		var stsResp *stssdk.AssumeRoleWithServiceIdentityResponse
		stsResp, err = stsManager.AssumeRoleWithServiceIdentity(credentialSpec.UserId, roleArn, defaultStsDurationSeconds)

		if err != nil {
			if _, err := r.updateStatus(credential, &dswv1.CredentialStatus{
				Status:       dswv1.CredentialStatusFailed,
				ErrorMessage: err.Error(),
			}); err != nil {
				return ctrl.Result{}, err
			}

			return ctrl.Result{}, nil
		}
		credentials := stsResp.Credentials
		credentialInfo = &dswv1.CredentialInfo{
			SecurityToken:   credentials.SecurityToken,
			AccessKeyId:     credentials.AccessKeyId,
			AccessKeySecret: credentials.AccessKeySecret,
			Expiration:      credentials.Expiration,
		}
	}

	if _, err := r.updateStatus(credential, &dswv1.CredentialStatus{
		Credential: credentialInfo,
		Status:     dswv1.CredentialStatusReady,
	}); err != nil {
		return ctrl.Result{}, err
	}

	logger.Info("refresh sts token")

	secretData := map[string][]byte{
		"sts.accessKeyId":     []byte(credentialInfo.AccessKeyId),
		"sts.accessKeySecret": []byte(credentialInfo.AccessKeySecret),
		"sts.token":           []byte(credentialInfo.SecurityToken),
	}

	foundSecret.Data = secretData
	if err := r.Update(context.TODO(), foundSecret); err != nil {
		return ctrl.Result{}, err
	}

	logger.Info(fmt.Sprintf("sts token will be refreshed after %d seconds", credentialSpec.RefreshInterval))
	// Refresh sts token after RefreshInterval (in seconds)
	return ctrl.Result{
		RequeueAfter: time.Duration(credentialSpec.RefreshInterval) * time.Second,
		Requeue:      true,
	}, nil
}

// SetupWithManager sets up the controller
func (r *CredentialReconciler) SetupWithManager(mgr ctrl.Manager) error {
	var controllerBuilder grayrelease.ControllerBuilder
	if r.Config.EnableGrayRelease {
		controllerBuilder = grayrelease.NewWrapperControllerManagedBy(mgr).
			WithExtraOptions(grayrelease.WrapperOptions{})
	} else {
		controllerBuilder = grayrelease.NewStandardControllerManagedBy(mgr)
	}
	return controllerBuilder.
		For(&dswv1.Credential{}).
		WithOptions(
			controller.Options{
				MaxConcurrentReconciles: r.Config.MaxConcurrentReconciles,
				RecoverPanic:            true,
			},
		).
		Complete(r)
}

func (r *CredentialReconciler) updateStatus(credential *dswv1.Credential, status *dswv1.CredentialStatus) (ctrl.Result, error) {
	err := k8sretry.RetryOnConflict(k8sretry.DefaultBackoff, func() error {
		foundCredential := &dswv1.Credential{}
		if err := r.Get(context.TODO(), types.NamespacedName{Name: credential.Name, Namespace: credential.Namespace}, foundCredential); err != nil {
			return err
		}

		if status.Credential != nil {
			foundCredential.Status.Credential = status.Credential
		}

		if status.Status != "" {
			if foundCredential.Status.Status != status.Status {
				r.EventRecorder.Event(foundCredential, "Normal", "UpdateStatus",
					fmt.Sprintf("Status updated to %s", status.Status))
			}
			foundCredential.Status.Status = status.Status
		}

		if status.ErrorMessage != "" {
			foundCredential.Status.ErrorMessage = status.ErrorMessage
		}

		now := metav1.Now()
		foundCredential.Status.LastUpdateTime = &now
		return r.Status().Update(context.TODO(), foundCredential)
	})
	return ctrl.Result{}, err
}
