/*
Copyright 2021 The PAI Authors
*/

package controllers

import (
	"context"
	"fmt"
	dswv1 "pai-mono/apis/dsw-controllers/v1"
	"pai-mono/pkg/dsw-controllers/utils"
	"pai-mono/pkg/util/grayrelease"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"k8s.io/client-go/tools/record"

	"pai-mono/pkg/dsw-controllers/configs"

	"github.com/go-logr/logr"
	"github.com/pkg/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	ctrl "sigs.k8s.io/controller-runtime"
)

const (
	idlenessTimeoutLabelKey     = "dsw.alibaba.com/idleness-timeout"
	idlenessTimeoutLabelValue   = "true"
	instanceNamespaceLabelKey   = "instance-namespace"
	idleInstanceCullerFinalizer = "idleinstancecullers.dsw.alibaba.com/finalizer"
)

// IdleInstanceCullerReconciler reconciles a IdleInstanceCuller object
type IdleInstanceCullerReconciler struct {
	client.Client
	Log                       logr.Logger
	Scheme                    *runtime.Scheme
	APIReader                 client.Reader
	Config                    *configs.Config
	EventRecorder             record.EventRecorder
	IdleInstanceCullerService *IdleInstanceCullerService
}

//+kubebuilder:rbac:groups=dsw.alibaba.com,resources=idleinstancecullers,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=dsw.alibaba.com,resources=idleinstancecullers/status,verbs=get;update;patch

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *IdleInstanceCullerReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := r.Log.WithValues("name", req.NamespacedName)
	// panic recover
	defer func() {
		if err := recover(); err != nil {
			logger.Info("recover from panic occurred when reconciling culler")
			pErr := errors.New(fmt.Sprintf("%v", err))
			logger.Error(pErr, "panic error occurred")
		}
	}()

	idleInstanceCuller := &dswv1.IdleInstanceCuller{}
	if err := r.Get(ctx, req.NamespacedName, idleInstanceCuller); err != nil {
		logger.Info("could not find culler")
		return ctrl.Result{}, client.IgnoreNotFound(err)
	}

	logger = utils.GetLogger(logger, idleInstanceCuller)
	// Check DeletionTimestamp for finalization
	if idleInstanceCuller.DeletionTimestamp.IsZero() {
		if !controllerutil.ContainsFinalizer(idleInstanceCuller, idleInstanceCullerFinalizer) {
			controllerutil.AddFinalizer(idleInstanceCuller, idleInstanceCullerFinalizer)
			if err := r.Update(context.Background(), idleInstanceCuller); err != nil {
				return ctrl.Result{}, err
			}
		}
	} else {
		if result, err := r.handleFinalizer(idleInstanceCuller); utils.HasErrorOrRequeue(result, err) {
			return result, err
		} else {
			r.EventRecorder.Event(idleInstanceCuller, corev1.EventTypeNormal, "Deleted", "Object finalizer is deleted")
			return ctrl.Result{}, nil
		}
	}

	// Check execution status gets the final state or not
	if idleInstanceCuller.Status.ExecutionStatus == dswv1.InstanceExecStatusTerminated {
		logger.Info("cancel reconcile because culler status is terminated", "ExecutionStatus", idleInstanceCuller.Status.ExecutionStatus, "culler", idleInstanceCuller.GetName())
		return ctrl.Result{}, nil
	}

	if r.preCheck(idleInstanceCuller) {
		return utils.Requeue(r.Config.MetricsPullInterval)
	}

	// Get corresponding dsw instance and check instance status
	instance := &dswv1.DswInstance{}
	err := r.Get(ctx, types.NamespacedName{Name: idleInstanceCuller.Spec.InstanceName, Namespace: req.Namespace}, instance)
	if err != nil {
		// if the dswInstance has been stopped, set the culler status as Terminated
		if apierrors.IsNotFound(err) {
			err = r.APIReader.Get(ctx, types.NamespacedName{Name: idleInstanceCuller.Spec.InstanceName, Namespace: req.Namespace}, instance)
			if err != nil {
				if apierrors.IsNotFound(err) {
					terminationTime := metav1.NewTime(time.Now())
					if _, err := r.IdleInstanceCullerService.updateStatus(idleInstanceCuller, &dswv1.IdleInstanceCullerStatus{
						ExecutionStatus:       dswv1.InstanceExecStatusTerminated,
						IdleTime:              0,
						LastSyncExeStatusTime: &terminationTime,
					}); err != nil {
						logger.Error(err, "Failed to terminate idleInstanceCuller")
						return ctrl.Result{}, err
					}
					if err := r.IdleInstanceCullerService.removeTimeoutLabel(idleInstanceCuller); err != nil {
						logger.Error(err, "Failed to remove timeout label")
						return ctrl.Result{}, err
					}
					return ctrl.Result{}, nil
				} else {
					logger.Error(err, "Failed to get corresponding dsw instance", "instanceName", idleInstanceCuller.Spec.InstanceName)
					return ctrl.Result{}, err
				}
			}
		} else {
			logger.Error(err, "Failed to get corresponding dsw instance", "instanceName", idleInstanceCuller.Spec.InstanceName)
			return ctrl.Result{}, err
		}
	}

	if instance.Status.Status != string(corev1.PodRunning) {
		logger.Info("The corresponding dsw instance", "instanceName", idleInstanceCuller.Spec.InstanceName, "status", instance.Status.Status)
		return utils.Requeue(r.Config.MetricsPullInterval)
	}

	// Check whether the labels contain the specified idleness-timeout key or not.
	labels := idleInstanceCuller.Labels
	if labels != nil {
		if value, exist := labels[idlenessTimeoutLabelKey]; exist && value == idlenessTimeoutLabelValue {
			logger.Info("Timeout after specified maxIdleTime for dswInstance")
			return utils.Requeue(r.Config.MetricsPullInterval)
		}
	}
	result, err := r.IdleInstanceCullerService.syncExecutionStatus(ctx, idleInstanceCuller, instance)
	if err != nil {
		logger.Error(err, "Failed to sync execution status", "instanceName", idleInstanceCuller.Spec.InstanceName)
	}
	return result, err
}

func (r *IdleInstanceCullerReconciler) preCheck(idleInstanceCuller *dswv1.IdleInstanceCuller) bool {
	logger := utils.GetLogger(r.Log, idleInstanceCuller)
	// Check execution status synchronization interval, keep the fix frequency to query metrics
	lastSyncTime := idleInstanceCuller.Status.LastSyncExeStatusTime
	now := time.Now()
	if lastSyncTime != nil && lastSyncTime.Add(time.Duration(r.Config.MetricsPullInterval)*time.Second).After(now) {
		logger.Info("cancel reconcile because it doesn't meet time interval", "lastSyncTime", lastSyncTime, "now", now)
		return true
	}
	return false
}

func (r *IdleInstanceCullerReconciler) handleFinalizer(idleInstanceCuller *dswv1.IdleInstanceCuller) (ctrl.Result, error) {
	if controllerutil.ContainsFinalizer(idleInstanceCuller, idleInstanceCullerFinalizer) {
		r.Log.Info("clean up idleInstanceCuller", "culler", idleInstanceCuller.GetName(), "instanceName",
			idleInstanceCuller.Spec.InstanceName)
		controllerutil.RemoveFinalizer(idleInstanceCuller, idleInstanceCullerFinalizer)
		return ctrl.Result{}, r.Update(context.Background(), idleInstanceCuller)
	}
	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *IdleInstanceCullerReconciler) SetupWithManager(mgr ctrl.Manager) error {
	if err := mgr.GetFieldIndexer().IndexField(context.Background(), &corev1.Pod{},
		"status.phase", func(rawObj client.Object) []string {
			pod := rawObj.(*corev1.Pod)
			return []string{string(pod.Status.Phase)}
		}); err != nil {
		return err
	}
	var controllerBuilder grayrelease.ControllerBuilder
	if r.Config.EnableGrayRelease {
		controllerBuilder = grayrelease.NewWrapperControllerManagedBy(mgr).
			WithExtraOptions(grayrelease.WrapperOptions{})
	} else {
		controllerBuilder = grayrelease.NewStandardControllerManagedBy(mgr)
	}

	return controllerBuilder.
		For(&dswv1.IdleInstanceCuller{}).
		WithOptions(controller.Options{
			MaxConcurrentReconciles: r.Config.MaxConcurrentReconciles,
			RecoverPanic:            true,
		}).
		Complete(r)
}
