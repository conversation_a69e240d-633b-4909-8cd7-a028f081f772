package configs

import (
	"context"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"
	"k8s.io/apimachinery/pkg/types"

	dsv1config "pai-mono/pkg/pai-dlc-operator/common/config"

	v1 "k8s.io/api/core/v1"
	ctrl "sigs.k8s.io/controller-runtime"
)

const (
	ClusterTypeMultiTenancy     = "ack-mts"
	ClusterTypeAsiInner         = "asi-inner"
	ClusterTypeAsiCloud         = "asi-cloud"
	ClusterTypeAsiLingjun       = "asi-lingjun"
	ClusterTypeLight            = "light"
	ClusterTypeLightAck         = "light-ack"
	ClusterTypeLightAckPure     = "light-ack-pro-pure"
	TenantSystemNamespace       = "tenant-system"
	NameOfConfigMapInASI        = "cloud-config"
	TypeAck                     = "ack"
	TypeAsi                     = "asi"
	PrometheusMetricsSourceType = "prometheus"
	CmsMetricsSourceType        = "cms"
)

const (
	EGPUType          = "eGPU"
	EGPUACKType       = "eGPU_ACK"
	RuntimeDocker     = "docker"
	RuntimeContainerd = "containerd"
)

const (
	IngressVersionV1      = "v1"
	IngressVersionV1Beta1 = "v1beta1"
)

const (
	DlinkPolicyDefault    = "default"
	DlinkPolicyCrossAZone = "cross-azone"
)

const (
	RoleArnTemplate = "acs:ram::%s:role/%s"
)

var GlobalConfig = Config{}

type Config struct {
	ClusterType string
	Region      string
	// enable the feature to hide .snapshot sub-folders for CPFS & BMCPFS data sources
	EnableHideCPFSSnapShot bool
	// enable to use pov on host infrastructure for cpfs and bmcpfs datasource
	EnableCPFSPovOnHost                bool
	OnlySupportPovOnHostInfraStructure bool
	// domain of nas inner api, for example: nasservice-inner-pre.cn-wulanchabu-oxs.aliyuncs.com
	NasInnerDomain string
	// The region id of the cpfs file system, for example: cn-wulanchabu-oxs
	CPFSRegionId                      string
	VpcId                             string
	VpcCidr                           string
	PodCidr                           string
	VswitchId                         string
	SecurityGroupId                   string
	AccessKeyId                       string
	AccessKeySecret                   string
	ResourceAccountId                 string
	ServiceAccountAccessKeyId         string
	ServiceAccountAccessKeySecret     string
	StsEndpoint                       string
	ServiceAccountId                  string
	AcrVpcEndpoint                    string
	AcrInstanceId                     string
	AcrDomain                         string
	IngressClass                      string
	DockerRegistryAuth                string
	DswImageSaverImage                string
	DefaultRole                       string
	DSWToECIRole                      string
	ServiceRoleChainRole              string
	IngressAuthUrl                    string
	IngressAuthSignin                 string
	IngressHost                       string
	UseQuotaIdAsNamespace             bool
	JobSchedulePolicy                 string
	DlinkPolicy                       string
	SidecarImage                      string
	SetupSidecarImage                 string
	ProxySidecarImage                 string
	DindSidecarImage                  string
	EnableCpfs                        bool
	TenantScopeKey                    string
	EnableOnDemandImage               bool
	EnableAsiQuota                    bool
	EnablePullAcrEEImage              bool
	EnableRenewAcrTempAuthToken       bool
	EnableIdleInstanceCuller          bool
	PrometheusEndpointUrl             string
	StepInPrometheusRangeQueries      string
	StepInListRangeMetrics            string
	MetricsPullInterval               int64
	MetricsSourceType                 string
	CreateTimeOutInMinute             int64
	QueueTimeOutInMinute              int64
	RecoverTimeOutInMinute            int64
	SaveImageTimeOutInMinute          int64
	SavingJobPreparingTimeOutInMinute int64
	CommittingTimeOutInMinute         int64
	ControllerStartTime               time.Time
	EnableVirtualGPUConfig            bool
	EnableGpuExporter                 bool
	GpuExporterType                   string
	EnableGpuSideCarInjection         bool
	EnableRDMASideCarInjection        bool
	VirtualGPUConfigType              string
	Runtime                           string
	IngressVersion                    string
	SockPath                          string
	LocalBinPath                      string
	ContainerdPath                    string
	EndpointNetwork                   string
	EndpointType                      string
	EnableMultiCluster                bool
	ClusterConfigSource               string
	WorkloadQueueType                 string
	GatewayNamespace                  string
	ECIMainEniCIDR                    string
	ECISecondaryEniCIDR               string
	EnablePrivateZone                 bool
	PrivateZoneNamePrefix             string
	ECIVMImageId                      string
	MaxConcurrentReconciles           int
	EnablePullImageByNsm              bool
	ASIDefaultResourcePoolName        string
	EnableHttps                       bool
	HttpsSecretName                   string
	ECISystemDiskFilesystem           string
	ECIImageCacheDiskFilesystem       string
	PodLimitedLockedMemory            string
	PodLimitedNoFiles                 string
	EnableWritableLayerSizeCheck      bool
	MaxWritableLayerSize              string
	EnableNodeErrorReport             bool
	ImagePullSecret                   string
	EnableAliyunEniResource           bool
	CredentialDefaultRoleName         string
	CredentialDefaultKey              string
	ServiceName                       string
	EnableSyncSecurityGroupRule       bool
	EnableGraySyncSecurityGroupRule   bool
	ShardName                         string
	EnableGrayRelease                 bool
	EnableGrayWatcher                 bool
	GrayModule                        string
	KubeClientQPS                     int
	KubeClientBurst                   int
	ConfigmapMountDir                 string
	// SyncSecurityGroupRuleUsersWhiteList is a comma separated list of users that will be synced the security group defined in quota (lingjun only)
	SyncSecurityGroupRuleUsersWhiteList string
	EnablePaiCredential                 bool
	ImageSaverNameTag                   string
	SidecarNameTag                      string
	EnableKmsDecryption                 bool
	KmsSecretMountDir                   string
	DisableDefaultACREndpoint           bool
	WorkspaceEndpoint                   string
	EnableSpotMetering                  bool
	EnablePaiMetrics                    bool
	UserCommandOssEndpoint              string
	UserCommandOssBucket                string
	EnableUserCommand                   bool
	EnableResourceManagerGateway        bool
	RmcoreEndpoint                      string
}

func (c *Config) WithMultiTenancy() bool {
	return c.ClusterType == ClusterTypeMultiTenancy || c.ClusterType == ClusterTypeAsiCloud || c.ClusterType == ClusterTypeAsiLingjun
}

func (c *Config) WithVpc() bool {
	return c.ClusterType == ClusterTypeMultiTenancy || c.ClusterType == ClusterTypeAsiCloud
}

func (c *Config) WithTenantApiServer() bool {
	return c.ClusterType == ClusterTypeMultiTenancy || c.ClusterType == ClusterTypeAsiCloud
}

func (c *Config) WithAsiInner() bool {
	return c.ClusterType == ClusterTypeAsiInner
}

func (c *Config) WithAsiCloud() bool {
	return c.ClusterType == ClusterTypeAsiCloud
}

func (c *Config) WithAsiLingjun() bool {
	return c.ClusterType == ClusterTypeAsiLingjun
}

func (c *Config) WithLight() bool {
	return strings.Contains(c.ClusterType, "light")
}

func (c *Config) GetK8SType() string {
	if strings.Contains(c.ClusterType, TypeAck) {
		return TypeAck
	}
	return TypeAsi
}

func (c Config) WithXingyunMultiCluster() bool {
	return c.EnableMultiCluster && c.ClusterConfigSource == "xingyun"
}

func (c *Config) WithSidecar() bool {
	return c.SidecarImage != ""
}

func (c *Config) WithSetupSidecar() bool {
	return c.SetupSidecarImage != ""
}

func (c *Config) WithProxySidecar() bool {
	return c.ProxySidecarImage != ""
}

func (c *Config) WithDindSidecar() bool {
	// currently, only Lingjun Cluster support Docker in Docker
	return c.WithAsiLingjun() && c.DindSidecarImage != ""
}
func (c Config) WithMixedQueue() bool {
	return c.WorkloadQueueType == "mixed-queue"
}

// GetClusterCIDR get the pod cidr from configmap in asi cloud clusters
func (c Config) GetClusterCIDR(mgr ctrl.Manager) (*string, error) {
	var clusterCidr string
	configMap := &v1.ConfigMap{}
	ctx := context.Background()
	objectName := ctrl.Request{
		NamespacedName: types.NamespacedName{
			Namespace: TenantSystemNamespace,
			Name:      NameOfConfigMapInASI,
		},
	}
	err := mgr.GetAPIReader().Get(ctx, objectName.NamespacedName, configMap)
	if err != nil {
		return nil, err
	}

	config := configMap.Data["config.ini"]
	configSlice := strings.Split(config, "\n")
	for _, value := range configSlice {
		if strings.Contains(value, "cs_cluster_cidr") {
			cidrs := strings.Split(value, " ")
			clusterCidr = strings.Trim(cidrs[len(cidrs)-1], " ")
			break
		}
	}

	clusterCidr = strings.Trim(clusterCidr, "\"")
	return &clusterCidr, nil
}

// ConvertConfig get config defined in dlc-controller
func (c Config) ConvertConfig() (*dsv1config.Config, error) {
	var config dsv1config.Config
	err := mapstructure.Decode(c, &config)
	return &config, err
}
