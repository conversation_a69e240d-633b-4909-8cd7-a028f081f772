package routers

import (
	"context"
	"fmt"
	"net/http"
	"pai-mono/pkg/pai-resource-service/backends"
	"pai-mono/pkg/pai-resource-service/clients/capacity_reservation"
	"pai-mono/pkg/pai-resource-service/middlewares"
	"pai-mono/pkg/pai-resource-service/reconcile"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"pai-mono/pkg/pai-resource-service/api"
	quotaapis "pai-mono/pkg/pai-resource-service/api/quota"
	config "pai-mono/pkg/pai-resource-service/config"
	"pai-mono/pkg/pai-resource-service/config/constants"
	"pai-mono/pkg/pai-resource-service/handlers"
	nodehandler "pai-mono/pkg/pai-resource-service/handlers/node"
	operationhandler "pai-mono/pkg/pai-resource-service/handlers/resource_operation"
	"pai-mono/pkg/pai-resource-service/utils"
)

type APIController interface {
	RegisterRoutes(routes *gin.RouterGroup)
}

func InitRouter() *gin.Engine {
	r := gin.New()

	r.Use(
		gin.Logger(),
		gin.Recovery(),
		func(context *gin.Context) {
			context.Header("Cache-Control", "no-store,no-cache")
		},
	)

	// Register OpenTelemetry HTTP middleware first to accurately measure all HTTP requests
	// including the time spent in other middleware functions
	if config.Config.Prometheus.EnableMetricsExport {
		if err := middlewares.RegisterOtelHttpMiddleware(r); err != nil {
			panic(err)
		}
	}

	r.NoRoute(
		utils.Redirect500,
		utils.Redirect403,
		utils.Redirect404,
	)
	// Register health check router before signature middleware config
	healthCheckRoute := r.Group("")

	if config.Config.Signature.Enable {
		r.Use(
			middlewares.CheckSignatureMiddleware(),
		)
	}

	store := cookie.NewStore([]byte("secret"))
	store.Options(sessions.Options{
		Path:     "/",
		HttpOnly: true,
		SameSite: http.SameSiteLaxMode,
	})
	r.Use(
		sessions.Sessions("loginSession", store),
	)

	// if header don't have a requestId, set a value
	r.Use(
		utils.SetRequestId(),
	)

	// load the implement types of abstract backend
	logStorageType := config.Config.LogStorageType

	// load the implement types of abstract backend
	metricStorageType := config.Config.MetricStorageType

	// load the implement types of abstract backend
	objectStorageType := config.Config.BackendStorageType

	logHandler, err := handlers.NewLogHandler(logStorageType)
	if err != nil {
		panic(err)
	}

	ntmRequestHandler, err := handlers.NewNTMRequestHandler()
	if err != nil {
		panic(err)
	}

	stockRequestHandler, err := handlers.NewStockRequestHandler()
	if err != nil {
		panic(err)
	}

	resourceGroupHandler, err := handlers.NewResourceGroupHandler()
	if err != nil {
		panic(err)
	}

	machineGroupHandler, err := handlers.NewMachineGroupHandler()
	if err != nil {
		panic(err)
	}

	metricsHandler, err := handlers.NewMetricHandler(metricStorageType, objectStorageType)
	if err != nil {
		panic(err)
	}

	quotaHandler, err := handlers.NewQuotaHandler()
	if err != nil {
		panic(err)
	}

	nodeHandler, err := nodehandler.NewNodeHandler()
	if err != nil {
		panic(err)
	}

	resourceOperationHandler, err := operationhandler.NewResourceOperationHandler()
	if err != nil {
		panic(err)
	}

	spotHandler, err := handlers.NewSpotHandler()
	if err != nil {
		panic(err)
	}

	vpcHandler, err := handlers.NewVPCHandler(config.Config.RegionId, config.Config.VPC.Endpoint)
	if err != nil {
		panic(err)
	}
	ecsStockHandler, err := handlers.NewEcsStockHandler(
		config.Config.RegionId,
		config.Config.ECS.Endpoint,
		config.Config.ResourceAccount.AccessKeyId,
		config.Config.ResourceAccount.AccessKeySecret,
		resourceGroupHandler,
		vpcHandler)
	if err != nil {
		panic(err)
	}

	tagInnerClient, err := backends.NewTagInnerClient(
		config.Config.RegionId,
		config.Config.ServiceAccount.AccessKeyId,
		config.Config.ServiceAccount.AccessKeySecret)

	if err != nil {
		panic(err)
	}

	ecsBackend, err := backends.NewECSBackendForResourceAccount(config.Config.RegionId)
	if err != nil {
		panic(err)
	}

	resourceLimitHandler, err := handlers.NewResourceLimitHandler()
	if err != nil {
		panic(err)
	}

	cacheHandler, err := handlers.NewCacheHandler()
	if err != nil {
		panic(err)
	}

	capacityLockHandler, err := handlers.NewCapacityLockHandler()
	if err != nil {
		panic(err)
	}

	// Initialize capacity lock reconcile controller only if enabled
	if config.Config.CapacityLockReconciler.Enabled {
		// Initialize capacity reservation client (critical dependency for reconciler)
		capacityReservationClient := capacity_reservation.GetClient()
		if capacityReservationClient == nil {
			panic(fmt.Errorf("failed to initialize capacity reservation client"))
		}

		// Create capacity lock reconcile controller
		capacityLockReconcileController, err := reconcile.NewCapacityLockReconcileController(
			config.Config.CapacityLockReconciler.Enabled,
			capacityLockHandler,
			capacityReservationClient,
		)
		if err != nil {
			panic(err)
		}

		// Start the reconcile controller
		ctx := context.Background()
		if err := capacityLockReconcileController.Start(ctx); err != nil {
			panic(err)
		}
		zap.S().Info("Capacity lock reconcile controller started successfully")
	} else {
		zap.S().Info("Capacity lock reconcile controller is disabled")
	}

	// Register api v1 customized routers.
	apiV1Routes := r.Group(constants.ApiV1Routes)

	ctrls := DefaultAPIV1Controllers(
		logHandler,
		ntmRequestHandler,
		stockRequestHandler,
		resourceGroupHandler,
		machineGroupHandler,
		metricsHandler,
		quotaHandler,
		nodeHandler,
		resourceOperationHandler,
		spotHandler,
		tagInnerClient,
		ecsStockHandler,
		ecsBackend,
		resourceLimitHandler,
		cacheHandler,
		capacityLockHandler,
	)

	for _, ctrl := range ctrls {
		ctrl.RegisterRoutes(apiV1Routes)
	}

	// Register health check router.
	ctrl := NewHealthAPIControllers()
	ctrl.RegisterRoutes(healthCheckRoute)

	if config.Config.Prometheus.EnableMetricsExport {
		// Register Prometheus metrics endpoint
		prometheusCtrl := api.NewPrometheusAPIsController()
		prometheusCtrl.RegisterRoutes(r.Group(""))
	}

	return r
}

func DefaultAPIV1Controllers(
	logHandler *handlers.LogHandler,
	ntmRequestHandler *handlers.NTMRequestHandler,
	stockRequestHandler *handlers.StockRequestHandler,
	resourceGroupHandler *handlers.ResourceGroupHandler,
	machineGroupHandler *handlers.MachineGroupHandler,
	metricHandler *handlers.MetricHandler,
	quotaHandler *handlers.QuotaHandler,
	nodeHandler *nodehandler.NodeHandler,
	resourceOperationHandler *operationhandler.ResourceOperationHandler,
	spotHandler *handlers.SpotHandler,
	tagInnerClient *backends.TagInnerClient,
	ecsStockHandler *handlers.EcsStockHandler,
	ecsBackend *backends.ECSBackend,
	resourceLimitHandler *handlers.ResourceLimitHandler,
	cacheHandler *handlers.CacheServiceHandler,
	capacityLockHandler *handlers.CapacityLockHandler,
) []APIController {

	return []APIController{
		api.NewNTMRequestAPIsController(ntmRequestHandler, ecsStockHandler, ecsBackend),
		api.NewStockRequestAPIsController(stockRequestHandler),
		api.NewResourceGroupController(resourceGroupHandler, metricHandler, tagInnerClient),
		api.NewMachineGroupController(machineGroupHandler, tagInnerClient, ntmRequestHandler, resourceGroupHandler),
		quotaapis.NewQuotaAPIsController(quotaHandler, resourceGroupHandler, metricHandler),
		api.NewNodeAPIsController(quotaHandler, resourceGroupHandler, nodeHandler, metricHandler),
		api.NewResourceOperationAPIsController(
			resourceOperationHandler, quotaHandler, resourceGroupHandler),
		api.NewQueueAPIsController(quotaHandler),
		api.NewTagAPIsController(resourceGroupHandler, machineGroupHandler, tagInnerClient),
		api.NewInnerTagAPIsController(tagInnerClient),
		api.NewSpotAPIsController(spotHandler),
		api.NewPodAPIsController(),
		api.NewResourceLimitAPIsController(resourceLimitHandler),
		api.NewDataCacheAPIsController(cacheHandler, quotaHandler),
		api.NewCapacityLockAPIController(capacityLockHandler),
	}
}

func NewHealthAPIControllers() APIController {
	return api.NewHealthAPIsController()
}
