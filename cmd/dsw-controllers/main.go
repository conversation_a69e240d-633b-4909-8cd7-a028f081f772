package main

import (
	"context"
	b64 "encoding/base64"
	"flag"
	"fmt"
	"os"
	"pai-mono/pkg/util/grayrelease"
	logUtil "pai-mono/pkg/util/log"
	paiCtrl "pai-mono/pkg/util/pai-controller-runtime"
	"pai-mono/pkg/util/paicredential"
	credentialClient "pai-mono/pkg/util/paicredential/client"
	rmcoreCli "pai-mono/pkg/util/rmcore/client"
	"pai-mono/pkg/util/spot"
	stsOption "pai-mono/pkg/util/sts"
	stsClient "pai-mono/pkg/util/sts/client"
	"path"

	quotasv1 "gitlab.alibaba-inc.com/unischeduler/api/apis/quotas/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"pai-mono/pkg/dsw-controllers/aliyun/sts"
	"pai-mono/pkg/dsw-controllers/configs"
	"pai-mono/pkg/dsw-controllers/controllers"
	featuresOption "pai-mono/pkg/dsw-controllers/features"
	"pai-mono/pkg/dsw-controllers/options"
	"pai-mono/pkg/dsw-controllers/utils"
	"pai-mono/pkg/dsw-controllers/utils/features"
	"pai-mono/pkg/util/kms"
	"time"

	"pai-mono/pkg/pai-dlc-operator/common/aliyun/cr"
	"pai-mono/pkg/pai-dlc-operator/common/aliyun/cs"
	"pai-mono/pkg/pai-dlc-operator/common/aliyun/ecs"
	"pai-mono/pkg/pai-dlc-operator/common/aliyun/vpc"
	"pai-mono/pkg/pai-dlc-operator/common/helper/cached"

	"go.uber.org/zap/zapcore"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("setup")
)

const (
	registryAuthTemplate = "{\"username\":\"%s\",\"password\":\"%s\"}"
	registryAuthBlank    = "dummy"
	kmsConfigFileName    = "kmsConfig.json"
)

func decryptConfigByKms(c *configs.Config) error {
	if c.KmsSecretMountDir == "" {
		return nil
	}

	configFile := path.Join(c.KmsSecretMountDir, kmsConfigFileName)
	_, err := os.Stat(configFile)
	if err != nil {
		setupLog.Info(err.Error() + "kms config file not found: " + configFile)
		return err
	}

	decryptClient, err := kms.NewDecryptClientFromFile(configFile)
	if err != nil {
		setupLog.Info(err.Error())
		return err
	}

	plaintext, err := decryptClient.Decrypt(c.AccessKeySecret)
	if err != nil {
		return err
	}
	setupLog.Info("AccessKeySecret:" + c.AccessKeySecret + " decrypt success")
	c.AccessKeySecret = plaintext

	plaintext, err = decryptClient.Decrypt(c.ServiceAccountAccessKeySecret)
	if err != nil {
		return err
	}
	setupLog.Info("ServiceAccountAccessKeySecret:" + c.ServiceAccountAccessKeySecret + " decrypt success")
	c.ServiceAccountAccessKeySecret = plaintext

	return nil
}

func main() {
	var metricsAddr string
	var enableLeaderElection bool
	var leaderElectionNamespace string

	var ackClusterId string
	var region string
	var accessKeyId string
	var accessKeySecret string
	var acrInstanceName string

	config := &configs.GlobalConfig
	flag.StringVar(&metricsAddr, "metrics-addr", ":8081", "The address the metrics endpoint binds to.")
	flag.BoolVar(&enableLeaderElection, "enable-leader-election", false,
		"Enable leader election for controller manager. Enabling this will ensure there is only one active controller manager.")
	flag.StringVar(&leaderElectionNamespace, "leader-election-namespace", "", "The leader election namespace")
	flag.StringVar(&config.IngressClass, "ingress-class", "nginx", "Support to use our own ingress controller")
	flag.StringVar(&config.ClusterType, "cluster-type", "ack-mts", "Cluster type such as: ack-mts, asi, ...")
	flag.StringVar(&ackClusterId, "ack-cluster-id", "", "The ack cluster id")
	flag.StringVar(&region, "region", "", "the region which this controller runs")
	flag.BoolVar(&config.EnableHideCPFSSnapShot, "enable-hide-cpfs-snapshot", false, "Enable the feature to hide .snapshot sub-folders for CPFS & BMCPFS data sources")
	flag.BoolVar(&config.EnableCPFSPovOnHost, "enable-cpfs-povonhost", false, "Enable to use pov on host infrastructure for dswinstance")
	flag.BoolVar(&config.OnlySupportPovOnHostInfraStructure, "only-support-pov-on-host-infrastructure", false, "Only support pov on host infrastructure")
	flag.StringVar(&config.NasInnerDomain, "nas-inner-domain", "", "The domain of nas inner api")
	flag.StringVar(&config.CPFSRegionId, "cpfs-region-id", "", "The region id of the cpfs file system, only needs to be set in cn-wulanchabu-oxs region")
	flag.StringVar(&config.DswImageSaverImage, "dsw-image-saver-image", "", "Image saver docker image")
	flag.StringVar(&config.DockerRegistryAuth, "docker-registry-auth", "", "Docker registry auth for docker push")
	flag.StringVar(&accessKeyId, "access-key-id", "", "The ak id for dsw resource account")
	flag.StringVar(&accessKeySecret, "access-key-secret", "", "The ak secret dsw resource account")
	flag.StringVar(&config.ResourceAccountId, "resource-account-id", "", "The uid of dsw resource account")
	flag.StringVar(&config.ServiceAccountAccessKeyId, "service-account-access-key-id", "", "The ak id for service account")
	flag.StringVar(&config.ServiceAccountAccessKeySecret, "service-account-access-key-secret", "", "The ak secret service account")
	flag.StringVar(&config.StsEndpoint, "sts-endpoint", "sts.aliyuncs.com", "The sts endpoint")
	flag.StringVar(&config.ServiceAccountId, "service-account-id", "", "The uid of dsw service account")
	flag.StringVar(&config.DefaultRole, "default-role", "AliyunPAIDSWDefaultRole", "The default role for dsw controller")
	flag.StringVar(&config.DSWToECIRole, "dsw-to-eci-role", "AliyunPAIDSWToECIRole", "The role for dsw pass user privilege to eci")
	flag.StringVar(&config.ServiceRoleChainRole, "service-role-chain-role", "RoleToAssumeCustomerRole", "The default service role chain")
	flag.StringVar(&config.IngressAuthUrl, "ingress-auth-url", "", "The ingress auth url")
	flag.StringVar(&config.IngressAuthSignin, "ingress-auth-signin", "", "The ingress auth signin url")
	flag.StringVar(&config.JobSchedulePolicy, "job-schedule-policy", "default", "The policy of scheduling job such as queue-unit, queue-job and default")
	flag.BoolVar(&config.UseQuotaIdAsNamespace, "use-quota-id-as-namespace", false, "Enable use quota id as the namespace of job or tensorboard")
	flag.StringVar(&acrInstanceName, "acr-instance-name", "dsw", "The default acr instance name")
	flag.StringVar(&config.IngressHost, "ingress-host", "", "host in ingress")
	flag.StringVar(&config.DlinkPolicy, "dlink-policy", "default", "User vpc dlink policy: default, cross-azone")
	flag.StringVar(&config.SidecarImage, "sidecar-image", "", "Sidecar image if any")
	flag.StringVar(&config.SetupSidecarImage, "setup-sidecar-image", "", "Setup sidecar image if any")
	flag.StringVar(&config.DindSidecarImage, "dind-sidecar-image", "", "Dind sidecar image if any")
	flag.BoolVar(&config.EnableCpfs, "enable-cpfs", false, "Enable cpfs support")
	flag.StringVar(&config.TenantScopeKey, "tenant-scope-key", "tenant.dsw.alibaba-inc.com", "The tenant scope key (as node selector)")
	flag.BoolVar(&config.EnableOnDemandImage, "enable-on-demand-image", false, "Enable on-demand image pull")
	flag.BoolVar(&config.EnableAsiQuota, "enable-asi-quota", false, "Enable asiquota")
	flag.StringVar(&config.ProxySidecarImage, "proxy-sidecar-image", "", "Proxy sidecar image, create a proxy sidecar for tunneling ssh")
	flag.BoolVar(&config.EnablePullAcrEEImage, "enable-pull-acree-image", false, "Enable pull images from ACR-EE registry")
	flag.BoolVar(&config.EnableRenewAcrTempAuthToken, "enable-renew-acr-temp-auth-token", false, "Enable refresh acr temp auth token")
	flag.BoolVar(&config.EnableIdleInstanceCuller, "enable-idle-instance-culler", false, "Enable idle instance culler")
	flag.StringVar(&config.PrometheusEndpointUrl, "prometheus-endpoint-url", "", "The http api endpoint url of prometheus")
	flag.StringVar(&config.StepInPrometheusRangeQueries, "step-in-prometheus-range-queries", "30s", "The step in range queries of prometheus")
	flag.StringVar(&config.StepInListRangeMetrics, "step-in-list-range-metrics", "10s", "The step in list range metrics of prometheus")
	flag.Int64Var(&config.MetricsPullInterval, "metrics-pull-interval", 180, "The interval of pulling metrics in seconds")
	flag.StringVar(&config.MetricsSourceType, "metrics-source-type", configs.PrometheusMetricsSourceType, "The metrics source type")
	flag.Int64Var(&config.CreateTimeOutInMinute, "create-time-out-in-minute", 20, "The duration of creation timeout")
	flag.Int64Var(&config.QueueTimeOutInMinute, "queue-time-out-in-minute", 20, "The duration of queue timeout")
	flag.Int64Var(&config.RecoverTimeOutInMinute, "recover-time-out-in-minute", 10080, "The duration of recover timeout")
	flag.Int64Var(&config.SaveImageTimeOutInMinute, "save-image-time-out-in-minute", 30, "The duration of save image timeout")
	flag.Int64Var(&config.SavingJobPreparingTimeOutInMinute, "saver-job-preparing-time-out-in-minute", 5, "The timeout of preparing saver job")
	flag.Int64Var(&config.CommittingTimeOutInMinute, "committing-time-out-in-minute", 120, "The timeout of committing dsw-notebook container")
	flag.BoolVar(&config.EnableVirtualGPUConfig, "enable-virtual-gpu-config", false, "enable virtual gpu")
	flag.BoolVar(&config.EnableGpuExporter, "enable-gpu-exporter", false, "enable gpu exporter")
	flag.StringVar(&config.GpuExporterType, "gpu-exporter-type", "GPU", "gpu exporter type: GPU, DCU")
	flag.StringVar(&config.VirtualGPUConfigType, "virtual-gpu-config-type", "eGPU", "type of virtual gpu")
	flag.StringVar(&config.Runtime, "runtime", "docker", "runtime of cluster")
	flag.StringVar(&config.IngressVersion, "ingress-version", "v1", "version of ingress")
	flag.StringVar(&config.SockPath, "sock-path", "/run/containerd/containerd.sock", "sock path for runtime")
	flag.StringVar(&config.LocalBinPath, "local-bin-path", "", "Local bin path")
	flag.StringVar(&config.ContainerdPath, "containerd-path", "", "Containerd path")
	flag.StringVar(&config.EndpointNetwork, "endpoint-network", "vpc", "Aliyun Api endpoint network type. public|vpc")
	flag.StringVar(&config.EndpointType, "endpoint-type", "regional", "Aliyun Api endpoint type. regional or EMPTY")
	flag.BoolVar(&config.EnableMultiCluster, "enable-multi-cluster", false, "enable multiple cluster")
	flag.StringVar(&config.ClusterConfigSource, "cluster-config-source", "xingyun", "source of cluster config. only xingyun")
	flag.StringVar(&config.WorkloadQueueType, "workload-queue-type", "queue-unit", "type of workload. queue-unit or mixed-queue")
	flag.StringVar(&config.GatewayNamespace, "gateway-namespace", "gloo-system", "namespace of gateway for ssh, such as gloo-gateway")
	flag.StringVar(&config.ECIMainEniCIDR, "eci-main-eni-cidr", "**********/10,**********/12,**********/16,**********/19", "ECI main eni CIDR for DLink.DefaultRoute=eth1")
	flag.StringVar(&config.ECISecondaryEniCIDR, "eci-secondary-eni-cidr", "", "ECI secondary eni CIDR for DLink.DefaultRoute=eth1")
	flag.BoolVar(&config.EnablePrivateZone, "enable-private-zone", false, "The option to enable PrivateZone as DNS-Server")
	flag.StringVar(&config.PrivateZoneNamePrefix, "private-zone-name-prefix", "svc.cluster.local.", "The prefix of PrivateZone Name")
	flag.StringVar(&config.ECIVMImageId, "eci-vm-image-id", "", "ECI VM Image id.")
	flag.IntVar(&config.MaxConcurrentReconciles, "max-concurrent-reconciles", 10, "max concurrent reconciles of ResourceGroup operator")
	flag.StringVar(&config.VpcId, "vpc-id", "", "vpc id")
	flag.StringVar(&config.VswitchId, "vswitch-id", "", "the vswitch id")
	flag.StringVar(&config.SecurityGroupId, "security-group-id", "", "the SecurityGroup Id")
	flag.BoolVar(&config.EnablePullImageByNsm, "enable-pull-image-by-nsm", false, "Enable pull images from ACR-EE registry by NSM NetworkService")
	flag.StringVar(&config.ASIDefaultResourcePoolName, "asi-default-resource-pool-name", "lingjun", "ASI default resource pool name")
	flag.BoolVar(&config.EnableGpuSideCarInjection, "enable-gpu-sidecar-injection", false, "Enable gpu exporter sidecar injection")
	flag.BoolVar(&config.EnableRDMASideCarInjection, "enable-rdma-sidecar-injection", false, "Enable rdma exporter sidecar injection")
	flag.BoolVar(&config.EnableHttps, "enable-https", false, "enable https in cluster")
	flag.StringVar(&config.HttpsSecretName, "https-secret-name", "cert-secret", "enable https in cluster")
	flag.StringVar(&config.ECISystemDiskFilesystem, "eci-system-disk-filesystem", "/dev/vda4", "system disk file system path of eci instances with cloud disk")
	flag.StringVar(&config.ECIImageCacheDiskFilesystem, "eci-image-cache-disk-filesystem", "/dev/disk/by-uuid/39d6ac87-caa5-4bdd-b31e-660301e51bab", "image cache file system path of eci instances with cloud disk")
	flag.StringVar(&config.PodLimitedLockedMemory, "pod-limited-locked-memory", "unlimited", "Pod limited locked memory size")
	flag.StringVar(&config.PodLimitedNoFiles, "pod-limited-no-files", "102400", "Pod limited number of files")
	flag.BoolVar(&config.EnableWritableLayerSizeCheck, "enable-writable-layer-size-check", false, "enable writable layer size check")
	flag.BoolVar(&config.EnableNodeErrorReport, "enable-node-error-report", false, "enable node error report")
	flag.StringVar(&config.MaxWritableLayerSize, "max-writable-layer-size", "10Gi", "max writable layer size when saving image")
	flag.StringVar(&config.ImagePullSecret, "image-pull-secret", "", "image pull secret name, split by ,")
	flag.BoolVar(&config.EnableAliyunEniResource, "enable-aliyun-eni-resource", false, "enable aliyun eni resource")
	flag.StringVar(&config.CredentialDefaultRoleName, "credential-default-role-name", "AliyunPAIDSWDefaultRole", "credential default role name")
	flag.StringVar(&config.CredentialDefaultKey, "credential-default-key", "0", "credential default key")
	flag.StringVar(&config.ServiceName, "service-name", "pai.aliyuncs.com", "service name")
	flag.BoolVar(&config.EnableSyncSecurityGroupRule, "enable-sync-security-group-rule", false, "Enable automatic synchronization of security group rules in Terway.")
	flag.BoolVar(&config.EnableGraySyncSecurityGroupRule, "enable-gray-sync-security-group-rule", false, "Enable gray by user for synchronization of security group rules")
	flag.StringVar(&config.SyncSecurityGroupRuleUsersWhiteList, "sync-security-group-rule-users-white-list", "", "Comma separated list of users that will be sync security group, only work in lingjun cluster")
	flag.BoolVar(&config.EnableKmsDecryption, "enable-kms-decryption", false, "enable kms decryption")
	flag.StringVar(&config.KmsSecretMountDir, "kms-secret-dir", "/workspace/dsw-kms-secret", "The directory to mounting the kms secret")
	flag.BoolVar(&config.EnablePaiCredential, "enable-pai-credential", false, "Enable PAI credential")
	flag.BoolVar(&config.DisableDefaultACREndpoint, "disable-default-acr", false, "Disable default ACR endpoint")
	flag.StringVar(&config.ImageSaverNameTag, "image-saver-name-tag", "", "Image saver name tag, e.g. dsw-image-saver:0.0.1")
	flag.StringVar(&config.SidecarNameTag, "sidecar-name-tag", "", "Sidecar name tag, e.g. dsw-sidecar:0.0.1")
	flag.StringVar(&config.WorkspaceEndpoint, "workspace-endpoint", "", "userdefine workspace endpoint")
	flag.BoolVar(&config.EnableSpotMetering, "enable-spot-metering", false, "enable spot metering")
	flag.BoolVar(&config.EnablePaiMetrics, "enable-pai-metrics", false, "Enable pai metrics")
	flag.BoolVar(&config.EnableGrayRelease, "enable-gray-release", false, "enable gray release")
	flag.BoolVar(&config.EnableGrayWatcher, "enable-gray-watcher", false, "enable gray watcher")
	flag.StringVar(&config.GrayModule, "gray-module", "", "gray module")
	flag.StringVar(&config.ShardName, "shard-name", "", "The shard name")
	flag.IntVar(&config.KubeClientQPS, "kube-client-qps", 100, "the maximum QPS to the api server from dsw client")
	flag.IntVar(&config.KubeClientBurst, "kube-client-burst", 200, "Maximum burst for throttle")
	flag.StringVar(&config.ConfigmapMountDir, "local-config-dir", "./dsw-config/", "The directory to mounting the configmap(dsw-config)")
	flag.StringVar(&config.UserCommandOssEndpoint, "user-command-oss-endpoint", "", "userdefine user command oss endpoint")
	flag.StringVar(&config.UserCommandOssBucket, "user-command-oss-bucket", "", "userdefine user command oss bucket")
	flag.BoolVar(&config.EnableUserCommand, "enable-user-command", false, "enable userdefine user command or not")
	flag.BoolVar(&config.EnableResourceManagerGateway, "enable-resource-manager-gateway", false, "Enable resource manager gateway")
	flag.StringVar(&config.RmcoreEndpoint, "rmcore-endpoint", "rmcore-inc-inner.cn-hangzhou.aliyuncs.com", "rmcore endpoint")
	flag.Parse()

	config.Region = region
	config.AccessKeyId = accessKeyId
	config.AccessKeySecret = accessKeySecret
	config.ControllerStartTime = time.Now()

	// Init FeatureGate
	ctx := features.WithFeatureGate(context.Background(), features.DefaultFeatureGate)

	ctrl.SetLogger(zap.New(func(o *zap.Options) {
		o.Development = true
		o.TimeEncoder = zapcore.RFC3339TimeEncoder
	}))

	if config.EnableKmsDecryption {
		err := decryptConfigByKms(config)
		if err != nil {
			setupLog.Error(err, "Unable to run kms decryption")
			os.Exit(1)
		}
	}

	if config.EnableAsiQuota {
		_ = quotasv1.SchemeBuilder.AddToScheme(scheme)
	}

	if config.WithVpc() {
		csManager, err := cs.NewCsManager(region, config.EndpointNetwork, config.EndpointType, config.AccessKeyId, config.AccessKeySecret)
		if err != nil {
			setupLog.Error(err, "Unable to start manager, create cs manager failed")
			os.Exit(1)
		}
		clusterInfo, err := csManager.DescribeClusterDetail(ackClusterId)
		if err != nil {
			setupLog.Error(err, "Unable to start manager, describe cluster detail failed", "ackClusterId", ackClusterId)
			os.Exit(1)
		}
		if config.VpcId == "" {
			config.VpcId = clusterInfo.VpcId
		}
		if config.VswitchId == "" {
			config.VswitchId = clusterInfo.VswitchId
		}
		config.PodCidr = clusterInfo.SubnetCidr

		vpcManager, err := vpc.NewVpcManager(region, config.EndpointNetwork, config.EndpointType, config.AccessKeyId, config.AccessKeySecret)
		if err != nil {
			setupLog.Error(err, "Unable to start manager, create vpc manager failed")
			os.Exit(1)
		}
		vpcInfo, err := vpcManager.DescribeVpcAttribute(clusterInfo.VpcId)
		if err != nil {
			setupLog.Error(err, "Unable to start manager, describe vpc attribute error", "vpcId", clusterInfo.VpcId)
			os.Exit(1)
		}
		config.VpcCidr = vpcInfo.CidrBlock
	}

	if acrInstanceName != "" {
		crManager, err := cr.NewCrManager(region, config.EndpointNetwork, config.EndpointType, config.AccessKeyId, config.AccessKeySecret)
		if err != nil {
			setupLog.Error(err, "unable to start manager")
			os.Exit(1)
		}
		acrVpcEndpoint, err := crManager.GetInstanceVpcEndpoint(acrInstanceName, config.VpcId)
		if err != nil {
			setupLog.Error(err, "unable to start manager")
			os.Exit(1)
		}
		acrInstanceId, err := crManager.GetInstanceID(acrInstanceName)
		if err != nil {
			setupLog.Error(err, "unable to start manager")
			os.Exit(1)
		}

		acrDomain, err := crManager.GetAcrSystemDomainInVpc(acrInstanceId, config.VpcId)
		if err != nil {
			setupLog.Error(err, "unable to start manager")
			os.Exit(1)
		}
		config.AcrVpcEndpoint = acrVpcEndpoint
		config.AcrInstanceId = acrInstanceId
		config.AcrDomain = acrDomain
	}

	if config.WithLight() {
		if config.DockerRegistryAuth == registryAuthBlank {
			config.DockerRegistryAuth = getDockerRegistryAuthFromEnv()
		}
	}
	if config.CPFSRegionId == "" {
		config.CPFSRegionId = region
	}
	if config.NasInnerDomain == "" && !config.OnlySupportPovOnHostInfraStructure {
		config.EnableCPFSPovOnHost = false
	}

	setupLog.Info("Config initialized successfully")

	mgrConfig := ctrl.GetConfigOrDie()
	mgrConfig.UserAgent = "dsw-controllers"
	mgrConfig.QPS = float32(config.KubeClientQPS)
	mgrConfig.Burst = config.KubeClientBurst

	// leader election id
	leaderElectionID := "9527.dsw.alibaba.com"
	if config.ShardName != "" {
		leaderElectionID = leaderElectionID + "-" + config.ShardName
	}

	mgrOption := ctrl.Options{
		Scheme:                     options.Scheme,
		MetricsBindAddress:         metricsAddr,
		LeaderElection:             enableLeaderElection,
		LeaderElectionID:           leaderElectionID,
		LeaderElectionNamespace:    leaderElectionNamespace,
		Port:                       9443,
		LeaderElectionResourceLock: resourcelock.ConfigMapsLeasesResourceLock,
		BaseContext:                func() context.Context { return ctx },
	}

	if config.WithAsiInner() {
		mgrOption.Namespace = utils.DswUserNamespace
	}

	// Init Manager
	mgr, err := ctrl.NewManager(mgrConfig, mgrOption)
	if config.EnablePaiMetrics {
		mgr = paiCtrl.NewManagerFrom(mgr)
	}
	if err != nil {
		setupLog.Error(err, "Unable to start manager, create manager error")
		os.Exit(1)
	}

	if config.EnableGrayRelease {
		var grayReleaseConfigManager *cached.ConfigMapManager
		grayReleaseConfigManager, err = cached.NewConfigMapManager(path.Join(config.ConfigmapMountDir, grayrelease.GrayReleaseConfigFileName), 5*time.Minute)
		if err != nil {
			setupLog.Error(err, "failed to load grayReleaseConfig")
			os.Exit(1)
		}
		if err := grayrelease.InitGrayUtil(config.EnableGrayRelease, config.EnableGrayWatcher, config.GrayModule, grayReleaseConfigManager.GetConfig(), mgr); err != nil {
			setupLog.Error(err, "failed to initialize gray util", "enableGrayRelease", config.EnableGrayRelease, "grayReleaseConfig", grayReleaseConfigManager.GetConfig())
			os.Exit(1)
		}
		setupLog.Info("gray release util initialized successfully", "enableGrayRelease", config.EnableGrayRelease, "grayReleaseConfig", grayReleaseConfigManager.GetConfig())
	}

	var ecsManager *ecs.EcsManager
	var cachedEcsManager *cached.CachedEcsManager
	if config.WithMultiTenancy() {
		e, err := ecs.NewEcsManager(config.Region, config.EndpointNetwork, config.EndpointType,
			config.AccessKeyId, config.AccessKeySecret)
		if err != nil {
			setupLog.Error(err, "Unable to create EcsManager")
			os.Exit(1)
		}
		ecsManager = e

		ce, err := cached.NewCachedEcsManager(config.Region, config.EndpointNetwork, config.EndpointType,
			config.AccessKeyId, config.AccessKeySecret)
		if err != nil {
			setupLog.Error(err, "Unable to create CachedEcsManager")
			os.Exit(1)
		}
		cachedEcsManager = ce
	}

	//Init Spot Manager
	spotInstanceManager := &spot.SpotInstanceManager{}
	setupLog.Info("Config EnableSpotMetering", "EnableSpotMetering", config.EnableSpotMetering)
	if config.EnableSpotMetering {
		spotConfig := &spot.Config{
			Region:          config.Region,
			AccessKeyId:     config.AccessKeyId,
			AccessKeySecret: config.AccessKeySecret,
		}
		spotInstanceManager, err = spot.NewSpotInstanceManager(
			mgr.GetClient(),
			mgr.GetCache(),
			ctrl.Log.WithName("controllers").WithName("SpotInstanceManager"),
			spotConfig,
		)
		if err != nil {
			setupLog.Error(err, "Unable to create SpotInstanceManager")
			os.Exit(1)
		}
		ctxSpot := context.WithValue(ctx, spot.ContextKeyOfSpot, "SpotInstanceManager")
		spotInstanceManager.Start(ctxSpot)
	}

	// create datasource service to be injected into controllers if needed, ex: dswInstanceController
	dataSourceService := controllers.NewDataSourceService(
		mgr.GetClient(),
		ctrl.Log.WithName("services").WithName("DataSource"),
		mgr.GetEventRecorderFor("datasource-service"),
		ecsManager,
		cachedEcsManager,
		config)

	stsService := sts.NewStsService(config.Region, config.StsEndpoint, ctrl.Log.WithName("service").WithName("Sts"))

	// create rmcoreClient
	var rmcoreClient rmcoreCli.Client = nil
	if config.EnableResourceManagerGateway {
		rmcoreClient, err = rmcoreCli.NewClientWithAccessKey(config.Region, config.AccessKeyId, config.AccessKeySecret, config.RmcoreEndpoint)
		if err != nil {
			setupLog.Error(err, "Unable to create rmcore client")
			os.Exit(1)
		}
	}

	// Notebook controller
	if err = (&controllers.NotebookReconciler{
		Client:            mgr.GetClient(),
		Log:               ctrl.Log.WithName("controllers").WithName("Notebook"),
		Scheme:            mgr.GetScheme(),
		APIReader:         mgr.GetAPIReader(),
		EventRecorder:     mgr.GetEventRecorderFor("notebook-controller"),
		Config:            config,
		DataSourceService: dataSourceService,
		RmcoreClient:      rmcoreClient,
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "Unable to create controller", "controller", "Notebook")
		os.Exit(1)
	}

	idleInstanceCullerService := controllers.NewIdleInstanceCullerService(
		mgr.GetClient(),
		ctrl.Log.WithName("services").WithName("IdleInstanceCullerService"),
		mgr.GetEventRecorderFor("idleinstanceculler-service"),
		config)

	// DswInstance controller
	stsCli, err := stsClient.NewClient(config.Region, config.ServiceAccountAccessKeyId, config.ServiceAccountAccessKeySecret, stsOption.ServiceRole(config.CredentialDefaultRoleName),
		stsOption.Endpoint(config.StsEndpoint))
	if err != nil {
		setupLog.Error(err, "Failed to create new sts client")
		os.Exit(1)
	}
	credMgrConfig := &paicredential.Config{
		ServiceName:               config.ServiceName,
		CredentialDefaultRoleName: config.CredentialDefaultRoleName,
		WorkspaceEndpoint:         config.WorkspaceEndpoint,
	}
	credentialMgr, err := credentialClient.NewCredentialManager(
		mgr.GetClient(),
		ctrl.Log.WithName("controllers").WithName("Credential"),
		credMgrConfig,
		stsCli)
	if err != nil {
		setupLog.Error(err, "Failed to create new credential manager")
		os.Exit(1)
	}
	if err = (&controllers.DswInstanceReconciler{
		Client:                    mgr.GetClient(),
		Log:                       logUtil.GetLogger(ctx).WithName("DswInstanceReconciler"),
		Scheme:                    mgr.GetScheme(),
		APIReader:                 mgr.GetAPIReader(),
		EventRecorder:             mgr.GetEventRecorderFor("dswinstance-controller"),
		Config:                    config,
		DataSourceService:         dataSourceService,
		IdleInstanceCullerService: idleInstanceCullerService,
		CredentialManager:         credentialMgr,
		SpotInstanceManager:       spotInstanceManager,
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "Unable to create controller", "controller", "Instance")
		os.Exit(1)
	}

	if config.EnableIdleInstanceCuller {
		if err = (&controllers.IdleInstanceCullerReconciler{
			Client:                    mgr.GetClient(),
			Log:                       ctrl.Log.WithName("controllers").WithName("IdleInstanceCuller"),
			Scheme:                    mgr.GetScheme(),
			APIReader:                 mgr.GetAPIReader(),
			Config:                    config,
			EventRecorder:             mgr.GetEventRecorderFor("idleinstanceculler-controller"),
			IdleInstanceCullerService: idleInstanceCullerService,
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "Unable to create controller", "controller", "IdleInstanceCuller")
			os.Exit(1)
		}
	}

	// Image controller (For image saving)
	if err = (&controllers.ImageReconciler{
		Client:        mgr.GetClient(),
		Log:           ctrl.Log.WithName("controllers").WithName("Image"),
		Scheme:        mgr.GetScheme(),
		APIReader:     mgr.GetAPIReader(),
		EventRecorder: mgr.GetEventRecorderFor("image-controller"),
		Config:        config,
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "Unable to create controller", "controller", "Image")
		os.Exit(1)
	}
	if features.DefaultFeatureGate.Enabled(featuresOption.EnablePccSaving) && features.DefaultFeatureGate.Enabled(featuresOption.EnablePccScheme) {
		setupLog.Info("Begin to setup ContainerSnapshot controller")
		if err = (&controllers.ContainerSnapshotReconciler{
			Client:        mgr.GetClient(),
			Log:           ctrl.Log.WithName("controllers").WithName("ContainerSnapshot"),
			Scheme:        mgr.GetScheme(),
			APIReader:     mgr.GetAPIReader(),
			EventRecorder: mgr.GetEventRecorderFor("containersnapshot-controller"),
			Config:        config,
		}).SetupWithManager(mgr); err != nil {
			setupLog.Error(err, "Unable to create controller", "controller", "ContainerSnapshot")
			os.Exit(1)
		}
	}

	// NasVolume Controller
	if err = (&controllers.NasVolumeReconciler{
		Client:        mgr.GetClient(),
		Log:           ctrl.Log.WithName("controllers").WithName("NasVolume"),
		Scheme:        mgr.GetScheme(),
		APIReader:     mgr.GetAPIReader(),
		EventRecorder: mgr.GetEventRecorderFor("nasvolume-controller"),
		Config:        config,
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "Unable to create controller", "controller", "NasVolume")
		os.Exit(1)
	}

	// Credential Controller
	if err = (&controllers.CredentialReconciler{
		Client:        mgr.GetClient(),
		Log:           ctrl.Log.WithName("controllers").WithName("Credential"),
		Scheme:        mgr.GetScheme(),
		EventRecorder: mgr.GetEventRecorderFor("credential-controller"),
		Config:        config,
		StsService:    stsService,
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "Unable to create controller", "controller", "Credential")
		os.Exit(1)
	}
	// +kubebuilder:scaffold:builder

	// Start Manager
	setupLog.Info("Starting manager")
	if err := mgr.Start(ctrl.SetupSignalHandler()); err != nil {
		setupLog.Error(err, "Problem running manager")
		os.Exit(1)
	}
}

func getDockerRegistryAuthFromEnv() string {
	username := os.Getenv("REGISTRY_USERNAME")
	password := os.Getenv("REGISTRY_PASSWORD")

	if username == "" || password == "" {
		return registryAuthBlank
	}

	authString := fmt.Sprintf(registryAuthTemplate, username, password)
	return b64.StdEncoding.EncodeToString([]byte(authString))
}
